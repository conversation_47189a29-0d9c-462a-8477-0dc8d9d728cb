#!/bin/bash

# =====================================================
# OpenAI 密钥批量验证主脚本
# 文件名: validate_keys.sh
# =====================================================

# 默认值配置 #https://ai-proxy.chatwise.app/openai/v1/models # https://api.openai.com/v1/models
DEFAULT_API_URL="https://ai-proxy.chatwise.app/openai/v1/models"
DEFAULT_KEY_PREFIX="sk"
DEFAULT_KEY_LENGTH="0"
DEFAULT_FAIL_KEYWORD="suspended"
REQUEST_TIMEOUT=15
REQUEST_DELAY=0.5

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 全局变量
declare -a ALL_KEYS
declare -a FORMAT_VALID_KEYS
declare -a API_VALID_KEYS
declare -a API_TEST_RESULTS  # 存储详细的API测试结果
KEYS_INPUT=""
API_URL=""
KEY_PREFIX=""
KEY_LENGTH=""
FAIL_KEYWORD=""
LENGTH_CHECK_MODE=""

# 使用说明
usage() {
    cat << 'EOF'
使用方法: 
  validate_keys.sh <keys_input> [选项]
  
必需参数:
  keys_input              密钥输入，可以是:
                         1) 多个密钥（用换行符分隔）
                         2) 包含密钥的文件路径

可选参数:
  --api-url <url>        API验证地址
  --prefix <prefix>      密钥前缀，使用"no_check"跳过前缀检查
  --length <length>      密钥长度要求
                         - 数字: 精确匹配长度
                         - 数字+: 长度大于该数字
                         - 数字-: 长度小于该数字
                         - 0: 不检查长度
  --fail-keyword <word>  响应中包含此关键词表示验证失败

示例:
  # 基本使用
  ./validate_keys.sh "aaa-key1
aaa-key2"

  # 从文件读取
  ./validate_keys.sh /path/to/keys.txt

  # 使用自定义参数
  ./validate_keys.sh keys.txt --api-url "https://api.example.com/v1/models" --prefix "sk-" --length "48+"
  
  # 不检查前缀
  ./validate_keys.sh keys.txt --prefix no_check --length "20+"
  
  # 自定义失败关键词
  ./validate_keys.sh keys.txt --fail-keyword "invalid"
EOF
    exit 1
}

# 打印函数
print_info() {
    echo -e "${BLUE}[信息]${NC} $1" >&2
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1" >&2
}

print_error() {
    echo -e "${RED}[错误]${NC} $1" >&2
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1" >&2
}



# 检测终端是否支持颜色
supports_color() {
    if [ -t 2 ] && [ "${TERM:-}" != "dumb" ]; then
        return 0
    else
        return 1
    fi
}

# 打印API测试详情到控制台
print_api_response() {
    local key="$1"
    local http_code="$2"
    local response="$3"
    local result="$4"

    echo "" >&2
    if supports_color; then
        echo "    ${BLUE}━━━ API响应详情 ━━━${NC}" >&2
        echo "    ${BLUE}密钥:${NC} ${key:0:20}***" >&2
        echo "    ${BLUE}HTTP状态码:${NC} $http_code" >&2
        echo "    ${BLUE}验证结果:${NC} $result" >&2
    else
        echo "    === API响应详情 ===" >&2
        echo "    密钥: ${key:0:20}***" >&2
        echo "    HTTP状态码: $http_code" >&2
        echo "    验证结果: $result" >&2
    fi

    if [ -n "$response" ]; then
        if supports_color; then
            echo "    ${BLUE}API响应内容:${NC}" >&2
        else
            echo "    API响应内容:" >&2
        fi

        # 格式化JSON响应以便阅读
        if echo "$response" | grep -q '^{'; then
            # 使用python或jq格式化JSON，如果都不可用则使用简单格式化
            if command -v python3 &>/dev/null; then
                echo "$response" | python3 -m json.tool 2>/dev/null | sed 's/^/        /' >&2 || \
                echo "$response" | sed 's/,/,\n        /g' | sed 's/{/{\n        /g' | sed 's/}/\n    }/g' >&2
            elif command -v jq &>/dev/null; then
                echo "$response" | jq . 2>/dev/null | sed 's/^/        /' >&2 || \
                echo "$response" | sed 's/,/,\n        /g' | sed 's/{/{\n        /g' | sed 's/}/\n    }/g' >&2
            else
                echo "$response" | sed 's/,/,\n        /g' | sed 's/{/{\n        /g' | sed 's/}/\n    }/g' >&2
            fi
        else
            echo "        $response" >&2
        fi
    else
        echo "    (无响应内容)" >&2
    fi

    if supports_color; then
        echo "    ${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━${NC}" >&2
    else
        echo "    ========================" >&2
    fi
}

# 解析长度参数
parse_length_param() {
    local length_str="$1"
    
    if [[ "$length_str" =~ ^([0-9]+)\+$ ]]; then
        KEY_LENGTH="${BASH_REMATCH[1]}"
        LENGTH_CHECK_MODE="greater"
    elif [[ "$length_str" =~ ^([0-9]+)-$ ]]; then
        KEY_LENGTH="${BASH_REMATCH[1]}"
        LENGTH_CHECK_MODE="less"
    elif [[ "$length_str" =~ ^[0-9]+$ ]]; then
        KEY_LENGTH="$length_str"
        LENGTH_CHECK_MODE="exact"
    else
        print_error "无效的长度参数格式: $length_str"
        print_error "支持的格式: 数字, 数字+, 数字-"
        exit 1
    fi
}

# 参数解析
parse_arguments() {
    # 检查是否有参数
    if [ $# -eq 0 ]; then
        usage
    fi
    
    # 第一个参数必须是keys_input
    KEYS_INPUT="$1"
    shift
    
    # 设置默认值
    API_URL="$DEFAULT_API_URL"
    KEY_PREFIX="$DEFAULT_KEY_PREFIX"
    KEY_LENGTH="$DEFAULT_KEY_LENGTH"
    FAIL_KEYWORD="$DEFAULT_FAIL_KEYWORD"
    LENGTH_CHECK_MODE="exact"
    
    # 解析命名参数
    while [ $# -gt 0 ]; do
        case "$1" in
            --api-url)
                if [ -z "$2" ] || [[ "$2" == --* ]]; then
                    print_error "参数 --api-url 需要一个值"
                    exit 1
                fi
                API_URL="$2"
                shift 2
                ;;
            --prefix)
                if [ -z "$2" ] || [[ "$2" == --* ]]; then
                    print_error "参数 --prefix 需要一个值"
                    exit 1
                fi
                KEY_PREFIX="$2"
                shift 2
                ;;
            --length)
                if [ -z "$2" ] || [[ "$2" == --* ]]; then
                    print_error "参数 --length 需要一个值"
                    exit 1
                fi
                parse_length_param "$2"
                shift 2
                ;;
            --fail-keyword)
                if [ -z "$2" ] || [[ "$2" == --* ]]; then
                    print_error "参数 --fail-keyword 需要一个值"
                    exit 1
                fi
                FAIL_KEYWORD="$2"
                shift 2
                ;;
            *)
                print_error "未知参数: $1"
                usage
                ;;
        esac
    done
    
    # 显示配置
    print_info "配置参数:"
    print_info "  API URL: $API_URL"

    if [ "$KEY_PREFIX" = "no_check" ]; then
        print_info "  前缀要求: 不检查"
    else
        print_info "  前缀要求: $KEY_PREFIX"
    fi

    if [ -z "$KEY_LENGTH" ] || [ "$KEY_LENGTH" -eq 0 ]; then
        print_info "  长度要求: 不检查"
    else
        case "$LENGTH_CHECK_MODE" in
            "exact") print_info "  长度要求: 等于 $KEY_LENGTH" ;;
            "greater") print_info "  长度要求: 大于 $KEY_LENGTH" ;;
            "less") print_info "  长度要求: 小于 $KEY_LENGTH" ;;
        esac
    fi

    print_info "  失败关键词: $FAIL_KEYWORD"
}

# 加载密钥
load_keys() {
    print_info "加载密钥中..."
    
    # 判断输入是文件路径还是直接的密钥内容
    if [ -f "$KEYS_INPUT" ]; then
        # 从文件读取
        print_info "从文件读取: $KEYS_INPUT"
        while IFS= read -r line; do
            # 跳过空行和注释
            if [[ -z "$line" ]] || [[ "$line" =~ ^[[:space:]]*# ]]; then
                continue
            fi
            line=$(echo "$line" | tr -d '\r' | xargs)
            if [ -n "$line" ]; then
                ALL_KEYS+=("$line")
            fi
        done < "$KEYS_INPUT"
    else
        # 直接解析密钥内容
        print_info "解析直接输入的密钥"
        while IFS= read -r line; do
            # 跳过空行和注释
            if [[ -z "$line" ]] || [[ "$line" =~ ^[[:space:]]*# ]]; then
                continue
            fi
            line=$(echo "$line" | tr -d '\r' | xargs)
            if [ -n "$line" ]; then
                ALL_KEYS+=("$line")
            fi
        done <<< "$KEYS_INPUT"
    fi
    
    if [ ${#ALL_KEYS[@]} -eq 0 ]; then
        print_error "没有找到任何有效密钥！"
        exit 1
    fi
    
    # 去重
    local unique_keys=()
    local seen=()
    for key in "${ALL_KEYS[@]}"; do
        local found=0
        for seen_key in "${seen[@]}"; do
            if [ "$key" = "$seen_key" ]; then
                found=1
                break
            fi
        done
        if [ $found -eq 0 ]; then
            unique_keys+=("$key")
            seen+=("$key")
        fi
    done
    
    local removed=$((${#ALL_KEYS[@]} - ${#unique_keys[@]}))
    if [ $removed -gt 0 ]; then
        print_warning "移除了 $removed 个重复密钥"
    fi
    ALL_KEYS=("${unique_keys[@]}")
    
    print_success "共加载 ${#ALL_KEYS[@]} 个唯一密钥"
}

# 检查密钥长度
check_key_length() {
    local key="$1"
    local key_len=${#key}

    # 如果KEY_LENGTH为空或为0，不检查长度
    if [ -z "$KEY_LENGTH" ] || [ "$KEY_LENGTH" -eq 0 ]; then
        return 0
    fi
    
    case "$LENGTH_CHECK_MODE" in
        "exact")
            if [ $key_len -eq $KEY_LENGTH ]; then
                return 0
            fi
            ;;
        "greater")
            if [ $key_len -gt $KEY_LENGTH ]; then
                return 0
            fi
            ;;
        "less")
            if [ $key_len -lt $KEY_LENGTH ]; then
                return 0
            fi
            ;;
    esac
    return 1
}

# 格式验证
validate_format() {
    print_info "开始格式验证..."
    
    for key in "${ALL_KEYS[@]}"; do
        local valid=true
        
        # 检查前缀（不区分大小写）
        if [ -n "$KEY_PREFIX" ] && [ "$KEY_PREFIX" != "no_check" ]; then
            local key_lower=$(echo "$key" | tr '[:upper:]' '[:lower:]')
            local prefix_lower=$(echo "$KEY_PREFIX" | tr '[:upper:]' '[:lower:]')
            if [[ ! "$key_lower" =~ ^"$prefix_lower" ]]; then
                valid=false
            fi
        fi
        
        # 检查长度
        if [ "$valid" = true ]; then
            if ! check_key_length "$key"; then
                valid=false
            fi
        fi
        
        if [ "$valid" = true ]; then
            FORMAT_VALID_KEYS+=("$key")
        fi
    done
    
    print_success "格式验证完成: ${#ALL_KEYS[@]} 个密钥中 ${#FORMAT_VALID_KEYS[@]} 个通过"

    if [ ${#FORMAT_VALID_KEYS[@]} -eq 0 ]; then
        print_error "没有密钥通过格式验证！"
        exit 1
    fi
}

# 验证单个密钥
validate_single_key() {
    local key="$1"
    local index="$2"
    local total="$3"

    printf "[%3d/%3d] 验证密钥 %s... " "$index" "$total" "${key:0:15}***" >&2

    # 发送请求
    local temp_file=$(mktemp)
    local temp_headers=$(mktemp)
    local http_code
    local curl_exit_code

    # 使用curl发送请求，同时获取响应头
    # 添加SSL选项以解决证书问题，并捕获错误信息
    local curl_error_file=$(mktemp)
    http_code=$(curl -s -w "%{http_code}" -o "$temp_file" -D "$temp_headers" \
        -X GET "$API_URL" \
        -H "Authorization: Bearer $key" \
        -H "Content-Type: application/json" \
        --connect-timeout "$REQUEST_TIMEOUT" \
        --max-time "$REQUEST_TIMEOUT" \
        --insecure \
        --cacert /etc/ssl/certs/ca-certificates.crt 2>"$curl_error_file" || \
        curl -s -w "%{http_code}" -o "$temp_file" -D "$temp_headers" \
        -X GET "$API_URL" \
        -H "Authorization: Bearer $key" \
        -H "Content-Type: application/json" \
        --connect-timeout "$REQUEST_TIMEOUT" \
        --max-time "$REQUEST_TIMEOUT" \
        --insecure 2>"$curl_error_file")
    curl_exit_code=$?

    # 读取curl错误信息
    local curl_error=""
    if [ -f "$curl_error_file" ]; then
        curl_error=$(cat "$curl_error_file" 2>/dev/null || echo "")
        rm -f "$curl_error_file"
    fi

    # 读取响应内容和响应头
    local response=""
    local headers=""
    if [ -f "$temp_file" ]; then
        response=$(cat "$temp_file" 2>/dev/null || echo "")
    fi
    if [ -f "$temp_headers" ]; then
        headers=$(cat "$temp_headers" 2>/dev/null || echo "")
    fi

    # 清理临时文件
    rm -f "$temp_file" "$temp_headers"

    # 记录详细信息到控制台和日志
    local result_status=""
    local detailed_info=""

    # 验证结果
    if [ "$curl_exit_code" -ne 0 ]; then
        result_status="CURL_ERROR"
        # 解释常见的curl错误码
        local error_desc=""
        case "$curl_exit_code" in
            6) error_desc="无法解析主机名" ;;
            7) error_desc="无法连接到服务器" ;;
            28) error_desc="操作超时" ;;
            35) error_desc="SSL连接错误" ;;
            51) error_desc="SSL证书验证失败" ;;
            52) error_desc="服务器未返回任何内容" ;;
            56) error_desc="网络接收数据失败" ;;
            60) error_desc="SSL证书验证失败 (可能是证书问题)" ;;
            *) error_desc="网络连接错误" ;;
        esac
        detailed_info="curl错误: $error_desc (退出码: $curl_exit_code)"
        if [ -n "$curl_error" ]; then
            detailed_info="$detailed_info - $curl_error"
        fi
        printf "%b\n" "${RED}✗ 网络错误 (curl: $curl_exit_code - $error_desc)${NC}" >&2
    elif [ -z "$http_code" ]; then
        result_status="TIMEOUT"
        detailed_info="请求超时"
        printf "%b\n" "${RED}✗ 请求超时${NC}" >&2
    elif [ "$http_code" = "200" ]; then
        # 检查响应是否包含失败关键词（不区分大小写）
        if [ -n "$FAIL_KEYWORD" ] && echo "$response" | grep -qi "$FAIL_KEYWORD"; then
            result_status="KEYWORD_FAIL"
            detailed_info="包含失败关键词: $FAIL_KEYWORD"
            printf "%b\n" "${RED}✗ 包含失败关键词: $FAIL_KEYWORD${NC}" >&2
        else
            result_status="VALID"
            detailed_info="API验证成功"
            printf "%b\n" "${GREEN}✓ 有效${NC}" >&2
            API_VALID_KEYS+=("$key")

            # 显示响应的关键信息
            if echo "$response" | grep -q '"data"'; then
                local model_count=$(echo "$response" | grep -o '"id"' | wc -l)
                printf "    ${GREEN}→ 可访问 %d 个模型${NC}\n" "$model_count" >&2
                detailed_info="$detailed_info, 可访问模型数: $model_count"
            fi
        fi
    else
        result_status="HTTP_ERROR"
        detailed_info="HTTP错误码: $http_code"
        printf "%b\n" "${RED}✗ 无效 [HTTP $http_code]${NC}" >&2

        # 显示错误详情
        if [ -n "$response" ]; then
            local error_msg=$(echo "$response" | grep -o '"message":"[^"]*"' | sed 's/"message":"\([^"]*\)"/\1/' | head -1)
            if [ -n "$error_msg" ]; then
                printf "    ${RED}→ 错误: %s${NC}\n" "$error_msg" >&2
                detailed_info="$detailed_info, 错误信息: $error_msg"
            fi
        fi
    fi

    # 打印详细的API响应信息到控制台
    print_api_response "$key" "$http_code" "$response" "$result_status"

    # 强制刷新输出缓冲区，确保响应信息立即显示
    exec 2>&2  # 刷新stderr

    # 存储测试结果供后续分析
    API_TEST_RESULTS+=("${key:0:15}***|$http_code|$result_status|$detailed_info")

    if [ "$result_status" = "VALID" ]; then
        return 0
    else
        return 1
    fi
}

# API批量验证
validate_api() {
    print_info "开始API验证 ${#FORMAT_VALID_KEYS[@]} 个密钥..."
    echo "========================================" >&2

    local index=1
    for key in "${FORMAT_VALID_KEYS[@]}"; do
        echo "" >&2  # 在每个密钥验证前添加空行
        validate_single_key "$key" "$index" "${#FORMAT_VALID_KEYS[@]}"

        # 添加分隔线（除了最后一个密钥）
        if [ $index -lt ${#FORMAT_VALID_KEYS[@]} ]; then
            echo "" >&2
            echo "    ────────────────────────────────────────" >&2
            echo "" >&2
            sleep "$REQUEST_DELAY"
        fi

        ((index++))
    done

    echo "" >&2
    echo "========================================" >&2
    print_success "API验证完成: ${#FORMAT_VALID_KEYS[@]} 个密钥中 ${#API_VALID_KEYS[@]} 个有效"
}

# 输出结果
output_results() {
    echo "" >&2
    echo "========================================" >&2
    print_info "测试结果汇总:"
    echo "========================================" >&2

    # 显示详细的测试结果统计
    local total_tested=${#FORMAT_VALID_KEYS[@]}
    local valid_count=${#API_VALID_KEYS[@]}
    local invalid_count=$((total_tested - valid_count))

    print_info "总共测试密钥: $total_tested"
    print_success "有效密钥数量: $valid_count"
    if [ $invalid_count -gt 0 ]; then
        print_error "无效密钥数量: $invalid_count"
    fi

    # 显示每个密钥的详细测试结果
    echo "" >&2
    print_info "详细测试结果:"
    echo "----------------------------------------" >&2
    for result in "${API_TEST_RESULTS[@]}"; do
        IFS='|' read -r key_masked http_code status detail <<< "$result"
        case "$status" in
            "VALID")
                printf "${GREEN}✓ %s [HTTP %s] %s${NC}\n" "$key_masked" "$http_code" "$detail" >&2
                ;;
            "HTTP_ERROR"|"KEYWORD_FAIL"|"TIMEOUT"|"CURL_ERROR")
                printf "${RED}✗ %s [HTTP %s] %s${NC}\n" "$key_masked" "$http_code" "$detail" >&2
                ;;
        esac
    done

    echo "========================================" >&2

    if [ ${#API_VALID_KEYS[@]} -eq 0 ]; then
        print_error "没有找到有效的密钥！"
        exit 1
    fi

    echo "" >&2
    print_success "✅ 验证通过的有效密钥列表:"
    echo "========================================" >&2

    # 显示有效密钥列表（带序号）
    local index=1
    for key in "${API_VALID_KEYS[@]}"; do
        printf "${GREEN}%2d. %s${NC}\n" "$index" "$key" >&2
        ((index++))
    done

    echo "========================================" >&2
    echo "" >&2

    print_info "📋 最终输出 - 有效密钥列表（每行一个）:" >&2
    echo "----------------------------------------" >&2

    # 输出到标准输出（供其他脚本捕获）- 每行一个密钥
    for key in "${API_VALID_KEYS[@]}"; do
        echo "$key"
    done

    echo "----------------------------------------" >&2

    # 保存到文件
    local output_file="valid_keys_$(date +%Y%m%d_%H%M%S).txt"
    printf "%s\n" "${API_VALID_KEYS[@]}" > "$output_file"
    print_info "💾 有效密钥已保存到文件: $output_file" >&2

    echo "" >&2
    print_success "🎉 验证完成！共找到 ${#API_VALID_KEYS[@]} 个有效密钥" >&2
}

# 主函数
main() {
    # 检查curl是否安装
    if ! command -v curl &> /dev/null; then
        print_error "需要安装 curl: sudo apt-get install curl"
        exit 1
    fi

    print_info "OpenAI API密钥批量验证工具"
    print_info "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"

    # 解析参数
    parse_arguments "$@"

    # 执行验证流程
    load_keys
    validate_format
    validate_api
    output_results
}

# 执行主函数
main "$@"