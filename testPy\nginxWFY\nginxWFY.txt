        # 使用正则匹配动态处理 /action_page-xxx 的请求
        location ~ ^/action_page-(?<app_name>[^/]+)(?<orig_app_path>/.*)?$ {

            # 处理 OPTIONS 预检请求 - 对接跨域请求
            if ($request_method = OPTIONS) {
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, HEAD, POST, PUT, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' $http_access_control_request_headers always;
                add_header 'Access-Control-Max-Age' 86400 always; # 预检缓存 一天内相同跨域请求无需重复发送 OPTIONS 预检请求（可选）
                add_header 'Content-Type' 'text/plain; charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
            if ($request_method !~ ^(GET|HEAD|POST|PUT|OPTIONS)$) {
                return 444;
            }

            # 通用 CORS 跨域头 - 针对非options请求
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, HEAD, POST, PUT, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' $http_access_control_request_headers always;
            # 安全请求头
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
            add_header X-Frame-Options           "SAMEORIGIN" always;
            add_header X-XSS-Protection          "1; mode=block" always;
            add_header X-Content-Type-Options    "nosniff" always;
            add_header Referrer-Policy           "no-referrer-when-downgrade" always;
            #add_header Content-Security-Policy   "default-src 'self';  connect-src 'self'; script-src 'self'; object-src 'none'; frame-ancestors 'none';" always;
            add_header Permissions-Policy        "geolocation=(), microphone=()" always;

            # --- 1. 定义正确的公共前缀和当前应用名 ---
            set $public_uri_prefix "/action_page-$app_name"; # e.g., /action_page-veloera
            set $current_app_name $app_name; # 方便 Lua 中使用

            # --- 2. 处理尾部斜杠，防止重定向循环 ---
            # 如果请求是 /action_page-appname (没有尾斜杠，orig_app_path 为空)
            # 或者 /action_page-appname/ (orig_app_path 为 "/") 且后端可能期望更具体的路径
            # 这里我们主要处理没有尾斜杠的情况，确保它被规范化。
            set $app_path $orig_app_path;
            if ($orig_app_path = "") {
                return 301 $scheme://$http_host$uri/;
            }
            # 如果 $orig_app_path 已经是 "/"，则 $app_path 保持为 "/"


            # --- START: ADD access_by_lua_block TO MODIFY UPSTREAM URI ---
            access_by_lua_block {
                -- ngx.var.public_uri_prefix is like "/action_page-appname"
                -- ngx.var.app_path is like "/some/actual/path" or "/" (guaranteed by prior redirect to have at least '/')
                local desired_uri_for_backend = ngx.var.public_uri_prefix .. ngx.var.app_path;

                -- Normalize potential double slashes.
                desired_uri_for_backend = string.gsub(desired_uri_for_backend, "//+", "/");

                -- Set the URI for the current request. This modified URI will be used by proxy_pass
                -- if proxy_pass does not specify a URI itself.
                ngx.req.set_uri(desired_uri_for_backend, false);
            }
            # --- END: ADD access_by_lua_block TO MODIFY UPSTREAM URI ---


            # 关键：禁用上游压缩，确保 sub_filter 能工作
            proxy_set_header Accept-Encoding "";

            # --- 4. 响应体内容替换 ---
            # sub_filter_types 适用于多种文本类型
            sub_filter_types text/plain text/html text/css text/xml application/xml application/json application/javascript application/xhtml+xml image/svg+xml;
            sub_filter_once off; # 确保替换所有匹配项

            # Lua 过滤器用于更复杂的响应体修改
            header_filter_by_lua_block {
                ngx.ctx.public_uri_prefix = ngx.var.public_uri_prefix;
                ngx.ctx.app_name = ngx.var.current_app_name;

                ngx.header["Content-Length"] = nil;
                -- Consider Content-Security-Policy adjustments if base-uri is used by backend
                -- local csp = ngx.header["Content-Security-Policy"]
                -- if csp then
                --    csp = csp:gsub("base-uri%s+[^;]+", "base-uri " .. ngx.var.scheme .. "://" .. ngx.var.http_host .. ngx.var.public_uri_prefix .. "/")
                --    ngx.header["Content-Security-Policy"] = csp
                -- end
            }


            body_filter_by_lua_block {
                local chunk = ngx.arg[1]
                local eof = ngx.arg[2]
                local public_prefix = ngx.ctx.public_uri_prefix -- e.g., /action_page-veloera
                local app_name = ngx.ctx.app_name

                if not chunk or not public_prefix or public_prefix == "" then
                    return -- No chunk or prefix, do nothing
                end

                ngx.ctx.buffered = (ngx.ctx.buffered or "") .. chunk
                ngx.arg[1] = nil -- Clear original chunk to avoid sending it prematurely

                if eof then
                    local body = ngx.ctx.buffered
                    ngx.ctx.buffered = nil -- Clear buffer

                    -- Lua pattern-escaped public_prefix for use in string.gsub
                    local escaped_public_prefix = public_prefix:gsub("([%(%)%.%%%+%-%*%?%[%]%^%$])", "%%%1")

                    -- 1. 保护特殊 URL (WebSocket, data URIs, blob URIs)
                    body = body:gsub('(WebSocket%s*%(%s*["\'])ws://', '%1WS_PROTECTED://')
                    body = body:gsub('(WebSocket%s*%(%s*["\'])wss://', '%1WSS_PROTECTED://')
                    body = body:gsub('(["\'%(%s])data:', '%1DATA_PROTECTED:')
                    body = body:gsub('(["\'%(%s])blob:', '%1BLOB_PROTECTED:')

                    -- 2. HTML: 注入或修正 <base href> 标签 (最关键的通用方案)
                    if ngx.header.content_type and ngx.header.content_type:find("text/html") then
                        local base_href_value = public_prefix .. "/" -- 必须以 / 结尾
                        if not body:find("<base%s+href") then
                            -- 尝试注入到 <head> 之后
                            body = body:gsub("(<head[^>]*>)", "%1\n<base href=\"" .. base_href_value .. "\">", 1)
                            if not body:find("<base%s+href") then -- 如果没有 <head>，尝试添加到文档开头
                                 body = "<!DOCTYPE html>\n<html>\n<head>\n<base href=\"" .. base_href_value .. "\">\n</head>\n<body>" .. body .. "\n</body>\n</html>"
                                 -- 更简单粗暴的方式，如果确定是html片段： body = "<base href=\"" .. base_href_value .. "\">\n" .. body
                            end
                        else
                            -- 如果存在，则修改它
                            body = body:gsub('(<base%s+href%s*=%s*["\'])([^"\']+)(["\'])', function(tag_start, old_href, tag_end)
                                if old_href:sub(1, #base_href_value) ~= base_href_value then
                                    return tag_start .. base_href_value .. tag_end
                                end
                                return tag_start .. old_href .. tag_end -- 已经是正确的，或是一个绝对URL
                            end)
                        end
                    end

                    -- 3. 处理各种属性中的绝对路径 URL (e.g., href="/path", src="/api/data")
                    --    这些不受 <base href> 影响，需要显式添加前缀
                    --    确保不会重复添加前缀
                    local attrs_to_prefix = {"href", "src", "action", "formaction", "data-url", "background", "poster"}
                    for _, attr in ipairs(attrs_to_prefix) do
                        -- Matches: attr="/path" but not attr="/action_page-appname/path"
                        -- Lua gsub with function allows more complex logic than pure regex for negative lookahead
                        body = body:gsub("(" .. attr .. "%s*=%s*[\"'])/([^/][^\"']*)", function(prefix_attr, path_val)
                            if not path_val:match("^" .. escaped_public_prefix .. "/") and
                               not path_val:match("^https?://") and
                               not path_val:match("^data:") and
                               not path_val:match("^blob:") and
                               not path_val:match("^javascript:") and
                               not path_val:match("^mailto:") and
                               not path_val:match("^tel:") and
                               not path_val:match("^#") then
                                return prefix_attr .. public_prefix .. "/" .. path_val
                            else
                                return prefix_attr .. "/" .. path_val -- Already prefixed or absolute/special
                            end
                        end)
                         -- Matches: attr="./path"
                        body = body:gsub("(" .. attr .. "%s*=%s*[\"'])%./([^\"']*)", "%1" .. public_prefix .. "/%2")
                    end

                    -- 4. 处理 CSS 中的 url()
                    -- url(/path)
                    body = body:gsub("(url%s*%((%s*[\"']?))/([^/][^\"')%s]*)", function(prefix_url, quote, path_val)
                        if not path_val:match("^" .. escaped_public_prefix .. "/") and
                           not path_val:match("^https?://") and
                           not path_val:match("^data:") and
                           not path_val:match("^blob:") then
                            return prefix_url .. public_prefix .. "/" .. path_val
                        else
                            return prefix_url .. "/" .. path_val
                        end
                    end)
                    -- url(path_no_slash_or_dot_slash) - these are relative to CSS file or <base href>
                    -- If <base href> is set correctly for HTML, this is often fine for inline styles.
                    -- For external CSS files, paths are relative to the CSS file's location.
                    -- If CSS is at /action_page-app/style.css, then url(img.png) correctly resolves to /action_page-app/img.png
                    -- This rule is more for JS constructing paths or inline styles if <base> is missing/problematic.
                    body = body:gsub("(url%s*%((%s*[\"']?))(?!(/|%./|%.%./|data:|blob:|https?://))([^\"')%s]+)", function(prefix_url, quote, path_val)
                        if not path_val:match("^" .. escaped_public_prefix .. "/") then
                             return prefix_url .. public_prefix .. "/" .. path_val
                        end
                        return prefix_url .. path_val -- Should not be hit if pattern is correct
                    end)


                    -- 5. 处理 JavaScript 中的常见路径模式
                    -- fetch('/api'), .get("/data"), .post('/submit'), new XMLHttpRequest().open("GET", "/info")
                    local js_funcs = { "fetch", "%.get", "%.post", "%.ajax%s*%([^%)]*%s*url%s*:%s*[\"']", "open%s*%([^,]*,%s*[\"']" }
                    for _, func_pattern in ipairs(js_funcs) do
                        -- Absolute paths: func("/path")
                        body = body:gsub("(" .. func_pattern .. ")/([^/][^\"']*)", function(prefix_func, path_val)
                            if not path_val:match("^" .. escaped_public_prefix .. "/") and not path_val:match("^https?://") then
                                return prefix_func .. public_prefix .. "/" .. path_val
                            else
                                return prefix_func .. "/" .. path_val
                            end
                        end)
                        -- Relative paths: func("./path")
                        body = body:gsub("(" .. func_pattern .. ")%./([^\"']*)", "%1" .. public_prefix .. "/%2")
                    end

                    -- More generic JS string literal replacements (use with caution, can be too broad)
                    -- Example: var path = "/some/resource"; location.assign('/new/page');
                    -- This tries to catch string literals starting with "/" that are not part of a longer path or URL.
                    body = body:gsub("([=(,%s]%s*[\"'])/([^/\"'%s][^\"']*)", function(prefix_char, path_val)
                        if not path_val:match("^" .. escaped_public_prefix .. "/") and
                           not path_val:match("^https?://") and
                           not path_val:match("^[a-zA-Z0-9_%-]+/") -- Avoids things like "module/component"
                           then
                            return prefix_char .. public_prefix .. "/" .. path_val
                        else
                            return prefix_char .. "/" .. path_val
                        end
                    end)

                    -- 6. 还原受保护的 URL
                    body = body:gsub('WS_PROTECTED://', 'ws://')
                    body = body:gsub('WSS_PROTECTED://', 'wss://')
                    body = body:gsub('DATA_PROTECTED:', 'data:')
                    body = body:gsub('BLOB_PROTECTED:', 'blob:')

                    -- 7. 清理可能产生的双斜杠 (e.g., /prefix//path -> /prefix/path)
                    body = body:gsub(escaped_public_prefix .. "//", public_prefix .. "/")
                    body = body:gsub("([%w%-%._~])//", "%1/") -- General double slash, but not for protocol part e.g. http://

                    ngx.arg[1] = body
                end
            }

            # 设置必要的代理头
            proxy_set_header X-Real-IP "";
            proxy_set_header X-Forwarded-For "";
            proxy_set_header X-Forwarded-Host "";
            proxy_set_header Forwarded "";

            proxy_set_header Cf-Connecting-Ip "";
            proxy_set_header Cf-Ipcountry "";
            proxy_set_header True-Client-IP "";
            proxy_set_header CF-RAY "";
            proxy_set_header CF-Visitor "";
            proxy_set_header CF-Request-ID "";
            proxy_set_header X-Forwarded-SSL "";
            proxy_set_header CDN-LOOP "";
            proxy_set_header Referrer "";

            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Original-URI    $current_req;

            proxy_set_header connection        "close";
            proxy_set_header cache-control     "max-age=0";
            proxy_set_header accept-language   $acc_lang;

            proxy_set_header Host   $app_name;
            proxy_set_header Origin $scheme://$app_name;

            # --- 5. 处理后端重定向 (Location header) ---
            # proxy_redirect off; # 先禁用默认行为，我们自己精确控制
            # 如果后端返回 Location: /some/path -> Location: /action_page-app_name/some/path
            proxy_redirect ~^/(?!action_page-)(.*)$ $public_uri_prefix/$1;
            # 如果后端返回 Location: http://backend_host/some/path -> Location: https://your_server_ip/action_page-app_name/some/path
            proxy_redirect ~^https?://[^/]+/(?!action_page-)(.*)$ $scheme://$http_host$public_uri_prefix/$1;
            # 如果后端返回的 Location 已经包含了正确的 public_uri_prefix，则不做任何修改 (这条规则是隐式的，因为上面的规则不会匹配)

            if ($current_app_name = "veloera") {
                proxy_pass http://0.0.0.0:20078$is_args$args; # MODIFIED from $app_path$is_args$args
            }


            # --- 7. 错误处理 (可选) ---
            proxy_intercept_errors on;
            # error_page 502 503 504 /50x.html;
            # location = /50x.html { root /usr/share/nginx/html; internal; }
        }