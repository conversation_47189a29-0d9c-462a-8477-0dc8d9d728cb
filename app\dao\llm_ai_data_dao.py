from app.dao.base_dao import BaseDAO
from app.models.llm_ai_data import LlmAiData

class LlmAiDataDAO(BaseDAO):
    """
    Data Access Object for the LlmAiData model.
    Inherits common CRUD and query methods from BaseDAO.
    """
    def __init__(self):
        """
        Initialize the DAO with the LlmAiData model.
        """
        super().__init__(LlmAiData)

    # Add LlmAiData specific query methods here if needed in the future.
    # For now, rely on the generic methods from BaseDAO. 