// Ensure moduleConfig is loaded before this script runs
if (typeof moduleConfig === 'undefined') {
    console.error('Error: moduleConfig is not defined. Make sure the config script runs before generic_datatable.js');
    // Optionally stop execution or provide fallback
}

// --- Define constants for filter operators ---
// Based on app/dao/base_dao.py and llm_ai_operate_dao.py capabilities
const filterOperators = {
    string: [
        { id: 'eq', name: '等于' },
        { id: 'neq', name: '不等于' },
        { id: 'contains', name: '包含' },
        { id: 'not_contains', name: '不包含' },
        { id: 'starts_with', name: '开头是' },
        { id: 'ends_with', name: '结尾是' },
        { id: 'is_null', name: '是 空' },
        { id: 'is_not_null', name: '不是 空' }
    ],
    number: [ // Includes integer, float, double, numeric/decimal
        { id: 'eq', name: '等于' },
        { id: 'neq', name: '不等于' },
        { id: 'gt', name: '大于' },
        { id: 'gte', name: '大于等于' },
        { id: 'lt', name: '小于' },
        { id: 'lte', name: '小于等于' },
        { id: 'is_null', name: '是 空' },
        { id: 'is_not_null', name: '不是 空' }
    ],
    date: [ // Includes datetime, date
        { id: 'eq', name: '等于' },
        { id: 'neq', name: '不等于' },
        { id: 'gt', name: '晚于' }, // Use more intuitive names for date/time
        { id: 'gte', name: '晚于或等于' },
        { id: 'lt', name: '早于' },
        { id: 'lte', name: '早于或等于' },
        { id: 'is_null', name: '是 空' },
        { id: 'is_not_null', name: '不是 空' }
    ],
     time: [ // Includes time
        { id: 'eq', name: '等于' },
        { id: 'neq', name: '不等于' },
        { id: 'gt', name: '晚于' },
        { id: 'gte', name: '晚于或等于' },
        { id: 'lt', name: '早于' },
        { id: 'lte', name: '早于或等于' },
        { id: 'is_null', name: '是 空' },
        { id: 'is_not_null', name: '不是 空' }
    ],
    boolean: [
        { id: 'eq', name: '等于' },
        // 'neq' is often equivalent to 'eq' with the opposite value for boolean
        // Keeping 'neq' might be confusing. Let's stick to 'eq'.
        // { id: 'neq', name: '不等于' }
    ]
};

$(document).ready(function() {
    // Check again inside ready, just in case
    if (typeof moduleConfig === 'undefined') {
         console.error('Fatal Error: moduleConfig is not defined even after document ready.');
         alert('页面配置加载失败，请联系管理员。'); // User feedback
         return; // Stop execution
    }

    console.log(`Initializing generic DataTable for module: ${moduleConfig.module_name}`);

    // Placeholder for DataTable instance
    var table = null;

    // --- Global Variables ---
    var advancedFilters = {}; // Object to hold advanced filter values
    let crudModal = null; // Holder for the Bootstrap Modal instance

    // --- Check for moduleConfig ---
    if (typeof moduleConfig === 'undefined' || !moduleConfig || !moduleConfig.module_name) {
        console.error("moduleConfig is not defined or incomplete. DataTable initialization aborted.");
        // Optionally display a user-friendly message on the page
        $('#dataTable').html('<div class="alert alert-danger">表格配置加载失败，请联系管理员。</div>');
        return; // Stop execution
    }
    console.log("moduleConfig loaded:", moduleConfig);

    // --- DataTable Renderers ---
    const dataTableRenderers = {
        default: function(data, type, row) {
            // Handle null/undefined/empty string consistently
            return (data === null || data === undefined || data === '') ? '-' : data;
        },
        booleanBadge: function(data, type, row) {
            // Renders boolean as colored badges (Yes/No)
            if (data === true || data === 1 || data === '1') {
                // Use text from moduleConfig if available, otherwise default
                const yesText = moduleConfig?.language_strings?.boolean_yes || '是';
                return `<span class="badge badge-success">${yesText}</span>`;
            } else if (data === false || data === 0 || data === '0') {
                const noText = moduleConfig?.language_strings?.boolean_no || '否';
                return `<span class="badge badge-danger">${noText}</span>`;
            }
            const unknownText = moduleConfig?.language_strings?.boolean_unknown || '未知';
            return `<span class="badge badge-secondary">${unknownText}</span>`;
        },
        booleanYesNo: function(data, type, row) {
            // Renders boolean as plain text (Yes/No)
            if (data === true || data === 1 || data === '1') { return moduleConfig?.language_strings?.boolean_yes || '是'; }
            if (data === false || data === 0 || data === '0') { return moduleConfig?.language_strings?.boolean_no || '否'; }
            return '-';
        },
        datetime: function(data, type, row) {
            // Formats ISO-like datetime strings to 'YYYY-MM-DD HH:MM:SS'
            if (!data) return '-';
            try {
                const date = new Date(data);
                if (isNaN(date.getTime())) { // More robust check for invalid date
                    console.warn("Invalid date received for datetime renderer:", data);
                    return '-';
                 }
                const year = date.getFullYear();
                const month = ('0' + (date.getMonth() + 1)).slice(-2);
                const day = ('0' + date.getDate()).slice(-2);
                const hours = ('0' + date.getHours()).slice(-2);
                const minutes = ('0' + date.getMinutes()).slice(-2);
                const seconds = ('0' + date.getSeconds()).slice(-2);
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            } catch (e) {
                console.error("Error formatting date:", data, e);
                return '-';
            }
        },
        syncStatus: function(data, type, row) {
            // Specific renderer for sync status (adjust texts/classes as needed)
            const syncNeededText = moduleConfig?.language_strings?.sync_needed || '需同步';
            const syncNotNeededText = moduleConfig?.language_strings?.sync_not_needed || '无需同步';
             if (data === 1 || data === '1' || data === true) { // Handle potential type variations
                 return `<span class="badge badge-warning">${syncNeededText}</span>`;
             } else { // Assuming 0, false, null, undefined mean no sync needed
                 return `<span class="badge badge-secondary">${syncNotNeededText}</span>`;
             }
         },
         actionsPlaceholder: function(data, type, row) {
             // Placeholder for action buttons (actual buttons implemented in Step 3.5)
             // Basic structure, can be enhanced later based on permissions/config
              const editTitle = moduleConfig?.language_strings?.edit_button || '编辑';
              const deleteTitle = moduleConfig?.language_strings?.delete_button || '删除';
             return `<div class="action-buttons">` +
                    `<button class="btn btn-sm btn-primary btn-edit" title="${editTitle}" data-id="${row.id}"><i class="fas fa-edit"></i></button> ` +
                    `<button class="btn btn-sm btn-danger btn-delete" title="${deleteTitle}" data-id="${row.id}"><i class="fas fa-trash"></i></button>` +
                    // Add placeholders for other potential actions if needed (e.g., view details)
                    '</div>';
         },
        // Add more renderers here as needed (e.g., currency, links, custom HTML)
    };

    // --- Dynamic Column Definition ---
    function defineColumns() {
        let dtColumns = [];
        const columnConfigs = moduleConfig.columns_config || [];
        const selectAllTitle = moduleConfig?.language_strings?.select_all || '全选';
        const actionsTitle = moduleConfig?.language_strings?.actions_column || '操作';


        // 1. Checkbox column (always first)
        dtColumns.push({
            data: null,
            orderable: false,
            searchable: false,
            className: 'select-checkbox-cell dt-body-center',
            // Use row.id for the checkbox value/data attribute
            render: function (data, type, row, meta) {
                 return `<input type="checkbox" class="select-item" data-id="${row.id}">`;
            },
            // Use a unique ID for the "select all" checkbox
            title: `<input type="checkbox" class="select-all" id="selectAll_${moduleConfig.module_name}" title="${selectAllTitle}">`
        });

        // 2. Dynamic data columns
        columnConfigs.forEach((item, index) => { // Add index for logging/debugging
            if (!item || !item.data) {
                console.warn(`Skipping invalid column config at index ${index}:`, item);
                return; // Skip invalid configuration items
            }
            let colDef = {
                data: item.data,
                name: item.name || item.data,
                title: item.title || item.name || item.data,
                orderable: item.orderable !== undefined ? item.orderable : true,
                visible: item.visible !== undefined ? item.visible : true, // Use config visibility as default
                className: item.className || '',
                render: dataTableRenderers.default // Always start with default
            };

            // Apply specific renderer if configured and exists
            if (item.render_func && dataTableRenderers[item.render_func]) {
                colDef.render = dataTableRenderers[item.render_func];
            } else if (item.render_func) {
                console.warn(`Renderer function '${item.render_func}' configured for column '${item.data}' but not found in dataTableRenderers. Using default.`);
            }

            dtColumns.push(colDef);
        });

        // 3. Actions column (if enabled, always last data-related column)
        const enableActions = moduleConfig.enable_actions_column === undefined ? true : moduleConfig.enable_actions_column; // Default true
        if (enableActions) {
            dtColumns.push({
                data: null,
                orderable: false,
                searchable: false,
                className: 'actions-cell dt-body-center',
                title: actionsTitle,
                render: dataTableRenderers.actionsPlaceholder // Use the dedicated placeholder
            });
        }

        console.log(`[${moduleConfig.module_name}] Defined columns:`, dtColumns);
        return dtColumns;
    }

    // --- State Saving and Isolation ---
    function getStateSaveOptions() {
         const stateKey = 'DataTables_State_' + moduleConfig.module_name + '_' + location.pathname;
         const keyPrefix = moduleConfig.module_name + '_';

         return {
             stateSave: true,
             stateDuration: moduleConfig.state_duration !== undefined ? moduleConfig.state_duration : 60 * 60 * 24, // Default 1 day (in seconds)

             stateSaveParams: function (settings, data) {
                 // console.debug(`[stateSaveParams ${moduleConfig.module_name}] Original data:`, JSON.parse(JSON.stringify(data)));
                 const prefixedData = {};
                 for (const key in data) {
                     if (data.hasOwnProperty(key)) {
                         prefixedData[keyPrefix + key] = data[key];
                     }
                 }
                 // Modify 'data' in place
                 Object.keys(data).forEach(key => delete data[key]);
                 Object.assign(data, prefixedData);
                 // console.debug(`[stateSaveParams ${moduleConfig.module_name}] Prefixed data:`, JSON.parse(JSON.stringify(data)));
             },

             stateLoadParams: function (settings, data) {
                 // console.debug(`[stateLoadParams ${moduleConfig.module_name}] Raw loaded data:`, JSON.parse(JSON.stringify(data)));
                 const originalData = {};
                 for (const key in data) {
                     // Only un-prefix keys that actually have our prefix
                     if (data.hasOwnProperty(key) && key.startsWith(keyPrefix)) {
                         const originalKey = key.substring(keyPrefix.length);
                         originalData[originalKey] = data[key];
                     } else if (data.hasOwnProperty(key)) {
                         // Keep keys that don't have the prefix (might be added by plugins?)
                         // Although stateSaveParams should have prefixed everything... be safe.
                         originalData[key] = data[key];
                         // console.warn(`[stateLoadParams ${moduleConfig.module_name}] Found unexpected unprefixed key: ${key}`);
                     }
                 }
                  // Check if we actually un-prefixed anything, if not, maybe it's old/invalid state?
                  if (Object.keys(originalData).length === 0 && Object.keys(data).length > 0) {
                      console.warn(`[stateLoadParams ${moduleConfig.module_name}] Loaded state object seems empty or invalid after un-prefixing. Resetting state?`, data);
                      // Potentially clear the state by not modifying 'data', letting DT use defaults.
                      // However, for now, let's just proceed with the empty object.
                      // Object.keys(data).forEach(key => delete data[key]); // Option to clear state fully
                  } else {
                      // Modify 'data' in place
                      Object.keys(data).forEach(key => delete data[key]);
                      Object.assign(data, originalData);
                  }
                 // console.debug(`[stateLoadParams ${moduleConfig.module_name}] Unprefixed data:`, JSON.parse(JSON.stringify(data)));
             },

             stateSaveCallback: function(settings, data) {
                 // 'data' is already prefixed by stateSaveParams
                  try {
                        // console.debug(`[stateSaveCallback ${moduleConfig.module_name}] Saving to ${stateKey}`);
                        localStorage.setItem(stateKey, JSON.stringify(data));
                  } catch (e) {
                        console.error("Could not save state to localStorage: ", e);
                        // Consider notifying user or disabling state saving temporarily
                  }
             },

             stateLoadCallback: function(settings) {
                  try {
                        const stateString = localStorage.getItem(stateKey);
                        // console.debug(`[stateLoadCallback ${moduleConfig.module_name}] Loading from ${stateKey}:`, stateString ? stateString.substring(0, 100) + '...' : null);
                        if (stateString) {
                            const parsedState = JSON.parse(stateString);
                             // Basic validation: Check if it's an object
                             if (typeof parsedState === 'object' && parsedState !== null) {
                                 return parsedState;
                             } else {
                                 console.warn(`[stateLoadCallback ${moduleConfig.module_name}] Invalid state loaded from ${stateKey}. Resetting state.`);
                                 localStorage.removeItem(stateKey); // Clear invalid state
                                 return null;
                             }
                        }
                        return null; // No state saved yet
                   } catch (e) {
                        console.error(`Could not load or parse state from localStorage key ${stateKey}: `, e);
                        // Attempt to clear potentially corrupted state
                         try { localStorage.removeItem(stateKey); } catch (removeError) { /* ignore */ }
                        return null; // Load default state on error
                  }
             }
         };
    }

    // --- Column Visibility Toggle ---
    function initColumnToggle() {
        if (!table) {
            console.warn("initColumnToggle called before table is initialized.");
            return;
        }
        const dropdownMenu = $('.column-toggle-dropdown .dropdown-menu');
        if (dropdownMenu.length === 0) {
            console.warn("Column toggle dropdown container (.column-toggle-dropdown .dropdown-menu) not found.");
            return;
        }
        dropdownMenu.empty(); // Clear previous items

        const keyPrefix = moduleConfig.module_name + '_dt_col_vis_'; // Specific prefix for visibility

        table.columns().every(function (index) {
            const column = this;
            const header = $(column.header());
             // Try getting title from settings first, then header text
             let title = table.settings()[0].aoColumns[index].sTitle || header.text();
             if (!title) { // Fallback if header is empty or not rendered yet
                  const colConfig = moduleConfig.columns_config ? moduleConfig.columns_config[index - 1] : null; // Adjust index for checkbox
                  title = colConfig ? (colConfig.title || colConfig.name || colConfig.data) : `列 ${index}`;
             }


            // Skip checkbox (index 0) and potentially actions column (last index)
            const generatedColumns = table.settings()[0].aoColumns;
            const isCheckboxColumn = index === 0;
            const actionsEnabled = moduleConfig.enable_actions_column === undefined || moduleConfig.enable_actions_column === true;
            const isActionsColumn = actionsEnabled && index === (generatedColumns.length - 1);

            if (isCheckboxColumn || isActionsColumn) {
                 // Optionally add disabled items for these? For now, skip.
                return;
            }

            const columnKey = keyPrefix + index;
            const isInitiallyVisible = column.visible();
            const savedState = localStorage.getItem(columnKey);
            let isChecked = isInitiallyVisible; // Default based on initial state

            // Apply saved state *before* creating checkbox if it exists
            if (savedState === '0' && isInitiallyVisible) {
                 // console.debug(`[initColumnToggle ${moduleConfig.module_name}] Setting column ${index} ('${title}') visibility to false from localStorage.`);
                 column.visible(false);
                 isChecked = false; // Update checkbox state to match
            } else if (savedState === '1' && !isInitiallyVisible) {
                 // console.debug(`[initColumnToggle ${moduleConfig.module_name}] Setting column ${index} ('${title}') visibility to true from localStorage.`);
                 column.visible(true);
                 isChecked = true; // Update checkbox state to match
            } else if (savedState !== null) {
                 // Saved state matches initial state, no action needed, but update isChecked
                 isChecked = (savedState === '1');
            }
            // If no saved state, isChecked remains the initial visibility

            // Create dropdown item using unique ID including module name
            const checkboxId = `toggle-col-${moduleConfig.module_name}-${index}`;
            const checkboxHtml = $(
                `<div class="custom-control custom-checkbox dropdown-item">
                   <input type="checkbox" class="custom-control-input column-visibility-toggle" id="${checkboxId}" data-column-index="${index}" ${isChecked ? 'checked' : ''}>
                   <label class="custom-control-label" for="${checkboxId}">${title}</label>
                 </div>`
            );

             // --- Event Binding using delegation ONCE ---
             // We bind to the dropdown menu itself, listening for changes on the inputs inside it.
             // Ensure this binding happens only once, perhaps outside the loop or using .off().
             // This approach avoids attaching many individual listeners.

             // Store reference to the column for the event handler (alternative to finding it by index)
             // checkboxHtml.find('input').data('dt-column', column); // Store jQuery object or index? Index is safer.

            dropdownMenu.append(checkboxHtml);
        });

         // Event Delegation: Bind listener to the menu, handle clicks on children
         // Use module-specific namespace for easy removal if needed
         const eventNamespace = `.colvis.${moduleConfig.module_name}`;
         dropdownMenu.off('change' + eventNamespace) // Remove previous listener for this module
                      .on('change' + eventNamespace, 'input.column-visibility-toggle', function() {
             const $checkbox = $(this);
             const columnIndex = parseInt($checkbox.data('column-index'), 10);
             const isVisible = $checkbox.prop('checked');
             const columnToToggle = table.column(columnIndex); // Get column by index

             if (columnToToggle) {
                 // console.debug(`[Column Toggle ${moduleConfig.module_name}] Changing visibility for column ${columnIndex} to ${isVisible}`);
                 columnToToggle.visible(isVisible);
                 // Save state to localStorage (prefixed)
                 const storageKey = keyPrefix + columnIndex;
                 localStorage.setItem(storageKey, isVisible ? '1' : '0');
             } else {
                  console.error(`Could not find column with index ${columnIndex} to toggle visibility.`);
             }
         });
    }

    // --- Custom Page Length Control ---
    function initCustomPageLength() {
        if (!table) {
            console.warn("initCustomPageLength called before table is initialized.");
            return;
        }

        const containerSelector = `#${table.table().node().id}_wrapper .dataTables_length`; // Target specific table wrapper
        const $lengthContainer = $(containerSelector);

        if ($lengthContainer.length === 0) {
            console.warn("DataTables length menu container not found:", containerSelector);
            return; // Cannot add control if container doesn't exist
        }

        const controlClass = 'custom-page-length-control';
        const inputClass = 'custom-length-input form-control-sm'; // Add BS class
        const buttonClass = 'apply-custom-length btn-sm'; // Add BS class
        const localStorageKey = moduleConfig.module_name + '_dt_custom_length';
        const controlIdSuffix = `_${moduleConfig.module_name}`; // Make IDs unique per module

        // Add controls only once per container
        if ($lengthContainer.find(`.${controlClass}`).length === 0) {
            console.log(`[${moduleConfig.module_name}] Adding custom page length control.`);
            const placeholderText = moduleConfig?.language_strings?.custom_length_placeholder || '条/页';
            const buttonText = moduleConfig?.language_strings?.custom_length_button || '确定';
            const customLengthHtml = `
                <div class="${controlClass} d-inline-flex align-items-center ml-2">
                    <input type="number" min="1" max="${moduleConfig.max_custom_page_length || 10000}"
                           class="form-control ${inputClass}"
                           id="customLengthInput${controlIdSuffix}"
                           placeholder="${placeholderText}"
                           style="width: 80px; text-align: right;">
                    <button class="btn btn-outline-secondary ${buttonClass}"
                            id="customLengthBtn${controlIdSuffix}"
                            type="button">
                        ${buttonText}
                    </button>
                     <div class="invalid-feedback" id="customLengthError${controlIdSuffix}" style="position: absolute; bottom: -18px; left: 0;"></div>
                </div>
            `;
            $lengthContainer.append(customLengthHtml).css({ 'position': 'relative' }); // Make parent relative for feedback positioning
        }

        const $input = $lengthContainer.find(`#customLengthInput${controlIdSuffix}`);
        const $button = $lengthContainer.find(`#customLengthBtn${controlIdSuffix}`);
        const $errorMsg = $lengthContainer.find(`#customLengthError${controlIdSuffix}`);


        // --- Event Binding (scoped to the specific controls) ---
        const eventNamespace = `.customlen.${moduleConfig.module_name}`;

        $button.off('click' + eventNamespace).on('click' + eventNamespace, function() {
            applyCustomLength($input, $errorMsg);
        });

        $input.off('keypress' + eventNamespace).on('keypress' + eventNamespace, function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                applyCustomLength($input, $errorMsg);
            }
        }).off('input' + eventNamespace).on('input' + eventNamespace, function() {
             // Real-time validation feedback
             validateLengthInput($input, $button, $errorMsg);
        });


        // --- Apply saved length on load ---
        const savedLength = localStorage.getItem(localStorageKey);
        if (savedLength) {
            const lengthInt = parseInt(savedLength, 10);
             // Validate saved length before applying
            if (lengthInt && lengthInt > 0 && lengthInt <= (moduleConfig.max_custom_page_length || 10000)) {
                console.log(`[${moduleConfig.module_name}] Applying saved custom page length: ${lengthInt}`);
                $input.val(lengthInt); // Set input value
                validateLengthInput($input, $button, $errorMsg); // Update button state

                // Apply to table. Use try-catch as table might not be fully ready?
                // Being in initComplete *should* be safe.
                try {
                     // Use draw(false) to keep the current page if possible
                    table.page.len(lengthInt).draw(false);
                } catch(e) {
                    console.error("Error applying saved page length:", e);
                }
            } else {
                console.warn(`[${moduleConfig.module_name}] Ignoring invalid saved page length: ${savedLength}`);
                localStorage.removeItem(localStorageKey); // Remove invalid saved value
            }
        } else {
             // Ensure button state is correct even if no saved value
             validateLengthInput($input, $button, $errorMsg);
        }
    }

    // Helper for applying custom length
    function applyCustomLength($input, $errorMsg) {
        if (!table) return;
        const $button = $input.siblings(`.${'apply-custom-length'}`); // Find button reliably

        if (!validateLengthInput($input, $button, $errorMsg)) {
            $input.trigger('focus'); // Focus invalid input
            return;
        }

        const customLength = parseInt($input.val(), 10);
        const localStorageKey = moduleConfig.module_name + '_dt_custom_length';
        const warningThreshold = moduleConfig.page_length_warning_threshold || 1000;

        if (customLength > warningThreshold) {
            const warningMsg = moduleConfig?.language_strings?.large_page_warning || `加载大量数据（超过 ${warningThreshold} 条）可能会影响浏览器性能，确定要继续吗？`;
            if (!confirm(warningMsg)) {
                return;
            }
        }

        console.log(`[${moduleConfig.module_name}] Applying custom page length: ${customLength}`);
        localStorage.setItem(localStorageKey, customLength);
        table.page.len(customLength).draw(); // Redraw the table
    }

    // Helper for validating length input
    function validateLengthInput($input, $button, $errorMsg) {
        const value = $input.val();
        const minVal = parseInt($input.attr('min') || '1', 10);
        const maxVal = parseInt($input.attr('max') || '10000', 10);
        let isValid = false;
        let errorText = '';

        if (value === '') {
             isValid = true; // Empty is not invalid, just disables button
             $button.prop('disabled', true);
        } else {
             const numValue = parseInt(value, 10);
             if (!isNaN(numValue) && numValue >= minVal && numValue <= maxVal) {
                 isValid = true;
                 $button.prop('disabled', false);
             } else if (isNaN(numValue) || numValue < minVal) {
                 errorText = `请输入${minVal}或更大的数字`;
                 $button.prop('disabled', true);
             } else if (numValue > maxVal) {
                 errorText = `最大允许 ${maxVal} 条`;
                 $button.prop('disabled', true);
             }
        }

        if (isValid) {
            $input.removeClass('is-invalid');
            $errorMsg.text('').hide();
        } else {
            $input.addClass('is-invalid');
            $errorMsg.text(errorText).show();
        }
        return isValid && value !== ''; // Return true only if valid AND not empty for apply action
    }


    // --- Main DataTable Initialization ---
    function initDataTable() {
        console.log("Initializing DataTable for:", moduleConfig.module_name);

        // 1. Get Dynamic Columns
        const columns = defineColumns();
        if (!columns || columns.length <= 1) { // Should have at least checkbox + 1 data/action col
            console.error("Failed to define columns or no columns configured. Aborting DataTable init.");
             $('#dataTable').html('<div class="alert alert-danger">表格列配置错误。</div>');
            return;
        }

        // 2. Get State Saving Options
        const stateSaveOptions = getStateSaveOptions();

        // 3. Calculate Default Sort Order
        let defaultSortColumnIndex = 1; // Default to first data column (index 1)
        const defaultSortField = moduleConfig.default_sort ? moduleConfig.default_sort[0] : (moduleConfig.columns_config[0]?.data || 'id'); // Use first config col or 'id'
        const defaultSortDir = moduleConfig.default_sort ? moduleConfig.default_sort[1] : 'asc';

        // Find the index in the *actually generated* columns array
        const foundIndex = columns.findIndex(col => col.data === defaultSortField && col.orderable !== false); // Ensure it's orderable
        if (foundIndex > 0) { // Check > 0 because index 0 is checkbox
             defaultSortColumnIndex = foundIndex;
             console.log(`Default sort set to column index ${defaultSortColumnIndex} (field: ${defaultSortField}, dir: ${defaultSortDir})`);
        } else {
            console.warn(`Default sort field '${defaultSortField}' not found or not orderable in generated columns. Defaulting to index 1.`);
            // Ensure index 1 is actually orderable, otherwise find the first orderable one
             const firstOrderableIndex = columns.findIndex((col, idx) => idx > 0 && col.orderable !== false);
             if (firstOrderableIndex > 0) {
                 defaultSortColumnIndex = firstOrderableIndex;
                 console.log(`Updated default sort to first orderable column index ${defaultSortColumnIndex} (field: ${columns[firstOrderableIndex].data})`);
             } else {
                  console.warn("No orderable columns found other than checkbox. Default sorting might not work as expected.");
                  defaultSortColumnIndex = 1; // Fallback, might error if column 1 not orderable
             }
        }

        // 4. Define Language Options
        const languageUrl = moduleConfig.language_url || '/static/other/datatables.chinese.json'; // Default path
        const languageOptions = {
             // Prefer URL if specified and valid, otherwise use inline defaults
             url: languageUrl, // DT will fetch this URL
             // Provide some inline defaults in case the URL fails or is not provided
             "processing": moduleConfig?.language_strings?.processing || "处理中...",
             "lengthMenu": moduleConfig?.language_strings?.lengthMenu || "显示 _MENU_ 项结果",
             "zeroRecords": moduleConfig?.language_strings?.zeroRecords || "没有匹配结果",
             "info": moduleConfig?.language_strings?.info || "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
             "infoEmpty": moduleConfig?.language_strings?.infoEmpty || "显示第 0 至 0 项结果，共 0 项",
             "infoFiltered": moduleConfig?.language_strings?.infoFiltered || "(由 _MAX_ 项结果过滤)",
             "search": moduleConfig?.language_strings?.search || "搜索:",
             "paginate": {
                 "first": moduleConfig?.language_strings?.paginate_first || "首页",
                 "previous": moduleConfig?.language_strings?.paginate_previous || "上页",
                 "next": moduleConfig?.language_strings?.paginate_next || "下页",
                 "last": moduleConfig?.language_strings?.paginate_last || "末页"
             },
             "aria": { // For accessibility
                 "sortAscending": moduleConfig?.language_strings?.sortAscending || ": 升序排列",
                 "sortDescending": moduleConfig?.language_strings?.sortDescending || ": 降序排列"
             },
              "buttons": { // Localize button text if possible
                  "copy": moduleConfig?.language_strings?.button_copy || "复制",
                  "excel": moduleConfig?.language_strings?.button_excel || "Excel",
                  "csv": moduleConfig?.language_strings?.button_csv || "CSV",
                  "print": moduleConfig?.language_strings?.button_print || "打印",
                   "colvis": moduleConfig?.language_strings?.button_colvis || "列可见性" // Default text for potential ColVis button
              }
         };


        // 5. Initialize DataTable
        try {
            table = $('#dataTable').DataTable({
                // Core settings
                processing: true, // Show processing indicator
                serverSide: true, // Enable server-side processing
                responsive: true, // Enable responsive extension

                // State saving (using our isolated options)
                ...stateSaveOptions,

                // Data source
                ajax: {
                    url: moduleConfig.api_base_url, // API endpoint from config
                    type: "POST", // Assuming POST method for server-side processing
                    contentType: "application/json", // Sending JSON
                    data: function (d) {
                        // d is the standard DataTables request object
                        // Add advanced filters if they exist
                        if (advancedFilters && Object.keys(advancedFilters).length > 0) {
                            d.advanced_filters = advancedFilters;
                        }
                        // Add CSRF token if available/needed
                        // d.csrf_token = getCsrfToken(); // Example placeholder

                        // Convert to JSON string for sending
                        // console.debug("DataTable request data:", d); // Log request data
                        return JSON.stringify(d);
                    },
                    dataFilter: function(data) {
                        // Process response before DataTables uses it (optional)
                         // console.debug("Raw response from server:", data);
                         try {
                             var json = JSON.parse(data);
                             // You could transform the response here if needed
                             // e.g., if error format needs adjustment
                             if (json.error) {
                                 console.error("Server returned an error:", json.error);
                                 // Display error to user?
                                 showToast('error', '数据加载失败: ' + json.error);
                             }
                             return data; // Return original JSON string
                         } catch (e) {
                             console.error("Error parsing server response:", e, data);
                             showToast('error', '无法解析服务器响应');
                             // Return a valid empty DataTables structure to prevent errors
                             return JSON.stringify({
                                 draw: parseInt($('#dataTable').DataTable().ajax.params().draw, 10) || 0,
                                 recordsTotal: 0,
                                 recordsFiltered: 0,
                                 data: [],
                                 error: "无法解析服务器响应"
                             });
                         }
                    },
                    error: function (xhr, errorType, errorThrown) {
                        console.error("DataTables AJAX error:", errorType, errorThrown, xhr.status, xhr.responseText);
                        let errorMsg = "加载数据时发生错误。";
                        if (xhr.status === 401 || xhr.status === 403) {
                            errorMsg = "您没有权限访问此数据或会话已过期，请重新登录。";
                        } else if (xhr.status === 500) {
                             errorMsg = "服务器内部错误，请稍后重试或联系管理员。";
                         } else if (errorType === 'parsererror') {
                             errorMsg = "无法解析服务器响应，数据格式可能不正确。";
                         }
                         // Display error using a toast or alert
                         showToast('error', errorMsg);

                         // Optionally clear the table and show message
                         $('#dataTable').DataTable().clear().draw(); // Clear table data
                         // Display error in table body?
                         $('#dataTable tbody').html(`<tr><td colspan="${columns.length}" class="text-center text-danger">${errorMsg}</td></tr>`);
                    }
                },

                // Columns definition (dynamic)
                columns: columns,

                // Default ordering (dynamic)
                order: [[defaultSortColumnIndex, defaultSortDir]],

                // DOM layout (match the template structure)
                // Bfrtip: B=Buttons, f=filtering input, r=processing display, t=table, i=info, p=pagination
                 dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" + // Length menu (l) and Filtering input (f)
                      "<'row'<'col-sm-12'tr>>" + // Table (t) and Processing indicator (r)
                      "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>" + // Info (i) and Pagination (p)
                      "<'row'<'col-sm-12'B>>", // Buttons (B) at the bottom

                // Buttons configuration
                buttons: [
                    {
                        extend: 'copyHtml5',
                        text: '<i class="fas fa-copy"></i> ' + (moduleConfig?.language_strings?.button_copy || '复制'),
                        titleAttr: '复制到剪贴板',
                        className: 'btn-secondary' // Bootstrap class
                    },
                    {
                        extend: 'excelHtml5',
                        text: '<i class="fas fa-file-excel"></i> ' + (moduleConfig?.language_strings?.button_excel || 'Excel'),
                        titleAttr: '导出为 Excel',
                        className: 'btn-success', // Bootstrap class
                        title: moduleConfig.export_file_name || document.title || 'export' // Filename
                    },
                    {
                        extend: 'csvHtml5',
                        text: '<i class="fas fa-file-csv"></i> ' + (moduleConfig?.language_strings?.button_csv || 'CSV'),
                        titleAttr: '导出为 CSV',
                        className: 'btn-info', // Bootstrap class
                        title: moduleConfig.export_file_name || document.title || 'export' // Filename
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> ' + (moduleConfig?.language_strings?.button_print || '打印'),
                        titleAttr: '打印表格',
                        className: 'btn-warning' // Bootstrap class
                    }
                    // Add ColVis button if needed, e.g., { extend: 'colvis', text: '列' }
                    // But we are using custom toggle, so maybe not needed here.
                ],

                // Language configuration
                language: languageOptions,

                // Pagination settings from config or defaults
                pageLength: moduleConfig.page_length || 10, // Default page length
                lengthMenu: moduleConfig.length_menu || [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]], // Length menu options

                // Callback functions
                initComplete: function(settings, json) {
                    console.log(`[${moduleConfig.module_name}] DataTable initComplete.`);
                    // --- Initialize components that depend on the table ---
                    initColumnToggle(); // Setup column visibility controls
                    initCustomPageLength(); // Setup custom page length controls

                    // --- Setup Select All / Individual Checkbox logic ---
                    setupCheckboxListeners();

                    // --- Trigger other initializations ---
                    // e.g., initAdvancedFilters(), initCrudButtons() - to be implemented later
                    initAdvancedFilterUI(); // Call the new function here
                },
                drawCallback: function( settings ) {
                    // Called every time the table is drawn
                    // console.debug(`[${moduleConfig.module_name}] DataTable drawCallback.`);
                    // Update select-all checkbox state after draw
                     updateSelectAllCheckboxState();
                     // Re-apply tooltips or other dynamic element initializations if needed
                     // $('[data-toggle="tooltip"]').tooltip();
                }

                // Other options:
                // scrollX: true, // Enable horizontal scrolling if needed
                // fixedHeader: true, // Fix header on scroll (requires extension)
            });

        } catch (error) {
             console.error("Fatal error initializing DataTable:", error);
             $('#dataTable').html('<div class="alert alert-danger">表格初始化失败，请检查浏览器控制台或联系管理员。</div>');
        }
    }

    // --- Checkbox Handling ---
    function setupCheckboxListeners() {
         const tableId = '#dataTable'; // Use the table ID
         const selectAllId = `#selectAll_${moduleConfig.module_name}`; // Unique select-all ID

         // Select All checkbox
         $(tableId).on('change', selectAllId, function() {
             const isChecked = $(this).prop('checked');
             // Select only checkboxes on the *current page* if using client-side processing,
             // or all selectable items if server-side (depends on use case).
             // For now, let's target visible items on the current page for simplicity.
             $(`${tableId} tbody .select-item`).prop('checked', isChecked).trigger('change'); // Trigger change for potential listeners
              console.log(`Select All toggled: ${isChecked}`);
         });

         // Individual item checkboxes
         $(tableId).on('change', 'tbody .select-item', function() {
             // Update "Select All" checkbox state based on individual checkboxes
              updateSelectAllCheckboxState();
         });
    }

     function updateSelectAllCheckboxState() {
         const tableId = '#dataTable';
         const selectAllId = `#selectAll_${moduleConfig.module_name}`;
         const $selectAllCheckbox = $(selectAllId);
         if (!$selectAllCheckbox.length) return; // Exit if checkbox doesn't exist

         const $itemCheckboxes = $(`${tableId} tbody .select-item`);
         const totalItems = $itemCheckboxes.length;
         const checkedItems = $itemCheckboxes.filter(':checked').length;

         if (totalItems === 0) {
              $selectAllCheckbox.prop('checked', false);
              $selectAllCheckbox.prop('indeterminate', false);
         } else if (checkedItems === totalItems) {
              $selectAllCheckbox.prop('checked', true);
              $selectAllCheckbox.prop('indeterminate', false);
         } else if (checkedItems > 0) {
              $selectAllCheckbox.prop('checked', false);
              $selectAllCheckbox.prop('indeterminate', true); // Partially selected
         } else {
              $selectAllCheckbox.prop('checked', false);
              $selectAllCheckbox.prop('indeterminate', false);
         }
     }

    // --- Utility Functions (Example: Toast Notifications) ---
    // Replace with your actual toast implementation (e.g., Toastr, Bootstrap Toasts)
    function showToast(type, message) {
        console.log(`[Toast ${type}]: ${message}`);
        // Example using hypothetical toast function:
        // toastr[type](message);
        // Or Bootstrap Toast:
        // $('.toast').toast('show'); // Needs correct setup
        alert(`[${type.toUpperCase()}] ${message}`); // Simple fallback
    }

    // --- START EXECUTION ---
    initDataTable(); // Initialize the main DataTable

    // --- Initialization ---
    initDataTable();
    initColumnToggle(); // Initialize column toggle after table is potentially loaded from state
    initCustomPageLength();
    initAdvancedFilterUI(); // Initialize the advanced filter UI
    setupModalInteractions(table, moduleConfig); // Setup modal after table init

    // --- Advanced Filter Event Handlers ---
    const $filterContainer = $('#filterContainer'); // Cache the container

    // Event delegation for adding a condition/rule
    $filterContainer.on('click', '.btn-add-condition', function(e) {
        e.preventDefault();
        console.log('Add Condition clicked'); // Debug log
        const $group = $(this).closest('.filter-group');
        const $conditionsContainer = $group.find('.filter-conditions').first(); // Target direct child
        const groupLevel = parseInt($group.data('level') || 0);
        const conditionId = generateId('condition_');
        const newConditionHTML = createFilterConditionHTML(conditionId, groupLevel + 1); // Rule level is group level + 1

        // Add visual indication before appending (optional)
        const $newCondition = $(newConditionHTML).hide();
        $conditionsContainer.append($newCondition);
        $newCondition.slideDown(200); // Animate addition
        console.log(`Appended condition ${conditionId} to group ${$group.attr('id')}`); // Debug log
    });

    // Event delegation for adding a nested group
    $filterContainer.on('click', '.btn-add-group', function(e) {
        e.preventDefault();
        console.log('Add Group clicked'); // Debug log
        const $parentGroup = $(this).closest('.filter-group');
        const $conditionsContainer = $parentGroup.find('.filter-conditions').first(); // Target direct child
        const parentGroupLevel = parseInt($parentGroup.data('level') || 0);
        const groupId = generateId('group_');
        const newGroupHTML = createFilterGroupHTML(groupId, parentGroupLevel + 1); // Nested group level + 1

        const $newGroup = $(newGroupHTML).hide();
        $conditionsContainer.append($newGroup);
        $newGroup.slideDown(200); // Animate addition
        console.log(`Appended group ${groupId} to group ${$parentGroup.attr('id')}`); // Debug log
    });

    // Event delegation for removing a rule or a group
    $filterContainer.on('click', '.btn-remove-item', function(e) {
        e.preventDefault();
        e.stopPropagation(); // Prevent event bubbling, e.g., nested group removal triggering parent removal
        console.log('Remove Item clicked'); // Debug log
        
        const $itemToRemove = $(this).closest('.filter-condition, .filter-group');
        const itemId = $itemToRemove.attr('id');
        const itemType = $itemToRemove.hasClass('filter-condition') ? 'Condition' : 'Group';

        if ($itemToRemove.length) {
            // Add confirmation later if needed
            console.log(`Removing ${itemType}: ${itemId}`); // Debug log
            $itemToRemove.slideUp(200, function() {
                $(this).remove();
                console.log(`${itemType} ${itemId} removed from DOM.`); // Debug log
            });
        } else {
            console.warn('Could not find item to remove.');
        }
    });

    // Event delegation for toggling group logic (AND/OR)
    $filterContainer.on('click', '.btn-logic', function(e) {
        e.preventDefault();
        const $button = $(this);
        
        // Already active? Do nothing.
        if ($button.hasClass('btn-primary')) { 
            return; 
        }

        const logic = $button.data('logic'); // 'AND' or 'OR'
        const $group = $button.closest('.filter-group');
        const groupId = $group.attr('id');

        // Update button styles
        $button.removeClass('btn-outline-primary').addClass('btn-primary');
        $button.siblings('.btn-logic').removeClass('btn-primary').addClass('btn-outline-primary');

        // Update group data attribute
        $group.attr('data-operator', logic); // Use .attr() for direct attribute manipulation

        console.log(`Group ${groupId} logic changed to: ${logic}`); // Debug log
    });

    // Event delegation for field selection change within a condition
    $filterContainer.on('change', '.filter-field', function(e) {
        const $fieldSelect = $(this);
        const fieldId = $fieldSelect.val();
        const $conditionRow = $fieldSelect.closest('.filter-condition');
        const conditionId = $conditionRow.attr('id');

        console.log(`Field changed in condition ${conditionId} to: ${fieldId}`); // Debug log

        if (!fieldId) {
            // Handle case where field is deselected (optional: clear operator/value)
            $conditionRow.find('.filter-operator-col').empty();
            $conditionRow.find('.filter-value-col').empty();
            return;
        }

        // Find field configuration to get the type
        const fieldConfig = moduleConfig.filter_fields_config.find(f => f.id === fieldId);
        if (!fieldConfig) {
            console.error(`Configuration not found for field ID: ${fieldId}`);
            return; // Or default to string?
        }
        const fieldType = fieldConfig.type || 'string';
        console.log(`Field type determined as: ${fieldType}`); // Debug log

        // Find operator and value containers for this condition row
        const $operatorContainer = $conditionRow.find('.filter-operator-col');
        const $valueContainer = $conditionRow.find('.filter-value-col');

        // Generate and replace operator select HTML
        const newOperatorHTML = createOperatorSelectHTML(fieldType);
        $operatorContainer.html(newOperatorHTML);
        console.log('Operator dropdown updated.'); // Debug log

        // Get the default operator from the newly added select
        const $newOperatorSelect = $operatorContainer.find('select');
        const defaultOperatorId = $newOperatorSelect.val(); // Get the first/default operator value

        // Generate and replace value input HTML based on field type and default operator
        const newValueHTML = createValueInputHTML(fieldType, defaultOperatorId);
        $valueContainer.html(newValueHTML);
        console.log(`Value input updated for operator: ${defaultOperatorId}`); // Debug log
    });

    // P2.1.2.6: 实现操作符选择变更处理
    $filterContainer.on('change', '.filter-operator', function(e) {
        const $operatorSelect = $(this);
        const operatorId = $operatorSelect.val();
        const $conditionRow = $operatorSelect.closest('.filter-condition');
        const conditionId = $conditionRow.attr('id');
        
        console.log(`Operator changed in condition ${conditionId} to: ${operatorId}`); // Debug log
        
        // Get the field to determine type
        const $fieldSelect = $conditionRow.find('.filter-field');
        const fieldId = $fieldSelect.val();
        
        if (!fieldId) {
            console.warn('Cannot update value input: No field selected.');
            return;
        }
        
        // Find field configuration to get type
        const fieldConfig = moduleConfig.filter_fields_config.find(f => f.id === fieldId);
        if (!fieldConfig) {
            console.error(`Configuration not found for field ID: ${fieldId}`);
            return;
        }
        const fieldType = fieldConfig.type || 'string';
        
        // Find value container for this condition row
        const $valueContainer = $conditionRow.find('.filter-value-col');
        
        // Generate and replace value input HTML based on field type and selected operator
        const newValueHTML = createValueInputHTML(fieldType, operatorId);
        $valueContainer.html(newValueHTML);
        console.log(`Value input updated for operator: ${operatorId}`); // Debug log
    });

    // P2.1.3: 实现过滤条件收集功能
    // 添加应用过滤器和重置过滤器按钮的事件处理
    $('#btnApplyAdvancedFilter').on('click', function(e) {
        e.preventDefault();
        
        // 收集过滤条件并更新DataTable
        const filterData = collectFilterConditions();
        if (filterData) {
            console.log('应用高级筛选:', filterData);
            advancedFilters = filterData; // 更新全局筛选条件对象
            
            // 保存筛选条件到localStorage
            saveFilterState(filterData);
            
            // 重新加载表格数据
            table.ajax.reload();
            
            // 关闭筛选面板（如果需要）
            $('#filterPanel').collapse('hide');
            
            // 显示成功消息
            showToast('success', '已应用高级筛选');
        }
    });
    
    // 重置筛选器按钮
    $('#btnResetAdvancedFilter').on('click', function(e) {
        e.preventDefault();
        console.log('重置高级筛选');
        
        // 清除全局筛选条件
        advancedFilters = {};
        
        // 清除localStorage中的筛选状态
        clearFilterState();
        
        // 重置UI（重新初始化筛选UI）
        initAdvancedFilterUI();
        
        // 重新加载表格数据
        table.ajax.reload();
        
        // 显示消息
        showToast('info', '已重置高级筛选');
    });

    console.log(`[${moduleConfig.module_name}] Generic DataTable initialization complete.`);
});

/**
 * 收集所有筛选条件，构建筛选对象
 * @returns {Object} 结构化的筛选对象
 */
function collectFilterConditions() {
    // 收集顶层组
    const $topLevelGroups = $('#topLevelGroups > .filter-group');
    
    if ($topLevelGroups.length === 0) {
        console.log('没有找到任何筛选组');
        return null;
    }
    
    // 构建结果对象
    const resultFilter = {
        groups: []
    };
    
    // 处理每个顶层组
    $topLevelGroups.each(function() {
        const groupFilter = collectGroupConditions($(this));
        if (groupFilter && (groupFilter.groups.length > 0 || groupFilter.rules.length > 0)) {
            resultFilter.groups.push(groupFilter);
        }
    });
    
    // 如果有多个顶层组，设置它们之间的关系（默认为AND）
    if (resultFilter.groups.length > 1) {
        resultFilter.operator = 'AND'; // 默认顶层组间关系为AND
    }
    
    console.log('收集到的筛选条件:', resultFilter);
    return resultFilter.groups.length > 0 ? resultFilter : null;
}

/**
 * 递归收集组内的条件
 * @param {jQuery} $group - 组元素的jQuery对象
 * @returns {Object} 组内的筛选条件对象
 */
function collectGroupConditions($group) {
    if (!$group || $group.length === 0) {
        return null;
    }
    
    // 获取组操作符（AND/OR）
    const operator = $group.attr('data-operator') || 'AND';
    
    // 创建组对象
    const groupFilter = {
        operator: operator,
        rules: [],
        groups: []
    };
    
    // 处理组内的每个直接子元素
    const $items = $group.find('> .filter-conditions > *');
    $items.each(function() {
        const $item = $(this);
        
        if ($item.hasClass('filter-condition')) {
            // 处理规则条件
            const rule = collectConditionRule($item);
            if (rule) {
                groupFilter.rules.push(rule);
            }
        } else if ($item.hasClass('filter-group')) {
            // 递归处理嵌套组
            const nestedGroup = collectGroupConditions($item);
            if (nestedGroup && (nestedGroup.groups.length > 0 || nestedGroup.rules.length > 0)) {
                groupFilter.groups.push(nestedGroup);
            }
        }
        // 忽略其他元素，如关系指示器等
    });
    
    return groupFilter;
}

/**
 * 收集单个条件的规则
 * @param {jQuery} $condition - 条件元素的jQuery对象
 * @returns {Object|null} 规则对象或null（如果条件无效）
 */
function collectConditionRule($condition) {
    if (!$condition || $condition.length === 0) {
        return null;
    }
    
    // 获取字段、操作符和值
    const $fieldSelect = $condition.find('.filter-field');
    const $operatorSelect = $condition.find('.filter-operator');
    const $valueInput = $condition.find('.filter-value');
    
    const fieldId = $fieldSelect.val();
    const operatorId = $operatorSelect.val();
    
    if (!fieldId || !operatorId) {
        console.warn('条件缺少字段或操作符:', fieldId, operatorId);
        return null;
    }
    
    // 对于不需要值的操作符（如is_null），值可以为空
    const noValueOperators = ['is_null', 'is_not_null'];
    let value = null;
    
    if (!noValueOperators.includes(operatorId)) {
        // 根据输入类型获取值
        if ($valueInput.length > 0) {
            if ($valueInput.is('select')) {
                value = $valueInput.val();
            } else {
                value = $valueInput.val();
                
                // 根据字段类型转换值
                const fieldConfig = moduleConfig.filter_fields_config.find(f => f.id === fieldId);
                if (fieldConfig) {
                    const fieldType = fieldConfig.type || 'string';
                    
                    // 转换数值类型
                    if (fieldType === 'number' || fieldType === 'integer' || 
                        fieldType === 'float' || fieldType === 'double' || 
                        fieldType === 'decimal' || fieldType === 'numeric') {
                        if (value !== '' && !isNaN(parseFloat(value))) {
                            value = parseFloat(value);
                        }
                    }
                    // 转换布尔值
                    else if (fieldType === 'boolean') {
                        value = value === 'true' || value === '1' || value === true;
                    }
                    // 日期、时间等类型保持字符串形式，后端会处理
                }
            }
        }
        
        // 如果操作符需要值但值为空，可能需要警告或跳过此条件
        if (value === '' || value === null) {
            console.warn(`条件 ${fieldId} ${operatorId} 需要值但未提供`);
            return null; // 或者返回一个带有空值的规则，取决于后端如何处理
        }
    }
    
    // 构建规则对象
    return {
        field: fieldId,
        operator: operatorId,
        value: value
    };
}

/**
 * 保存筛选状态到localStorage
 * @param {Object} filterData - 筛选条件对象
 */
function saveFilterState(filterData) {
    if (!filterData) return;
    
    try {
        const key = `${moduleConfig.module_name}_advancedFilter`;
        const filterJson = JSON.stringify(filterData);
        localStorage.setItem(key, filterJson);
        console.log(`筛选状态已保存到 ${key}`);
    } catch (e) {
        console.error('保存筛选状态时出错:', e);
    }
}

/**
 * 从localStorage加载筛选状态
 * @returns {Object|null} 保存的筛选条件或null
 */
function loadFilterState() {
    try {
        const key = `${moduleConfig.module_name}_advancedFilter`;
        const filterJson = localStorage.getItem(key);
        if (filterJson) {
            return JSON.parse(filterJson);
        }
    } catch (e) {
        console.error('加载筛选状态时出错:', e);
    }
    return null;
}

/**
 * 清除保存的筛选状态
 */
function clearFilterState() {
    try {
        const key = `${moduleConfig.module_name}_advancedFilter`;
        localStorage.removeItem(key);
        console.log(`筛选状态已从 ${key} 清除`);
    } catch (e) {
        console.error('清除筛选状态时出错:', e);
    }
}

/**
 * 从筛选对象重建筛选UI
 * @param {Object} filterData - 筛选条件对象
 */
function rebuildFilterUIFromState(filterData) {
    if (!filterData || !filterData.groups || filterData.groups.length === 0) {
        // 没有有效的筛选数据，使用默认UI
        return false;
    }
    
    // 清除现有UI
    $('#topLevelGroups').empty();
    
    // 递归构建组
    filterData.groups.forEach((groupData, index) => {
        // 创建顶层组
        const groupId = generateId('group_');
        const $group = $(createFilterGroupHTML(groupId, 0, groupData.operator)).appendTo('#topLevelGroups');
        
        // 设置组操作符
        $group.attr('data-operator', groupData.operator);
        $group.find(`.btn-logic[data-logic="${groupData.operator}"]`)
            .addClass('btn-primary').removeClass('btn-outline-primary')
            .siblings('.btn-logic')
            .removeClass('btn-primary').addClass('btn-outline-primary');
            
        // 递归添加规则和子组
        rebuildGroupContent($group, groupData);
    });
    
    console.log('从保存状态重建筛选UI完成');
    return true;
}

/**
 * 递归重建组内容
 * @param {jQuery} $group - 组元素的jQuery对象
 * @param {Object} groupData - 组数据
 */
function rebuildGroupContent($group, groupData) {
    const $conditionsContainer = $group.find('> .filter-conditions');
    const groupLevel = parseInt($group.data('level') || 0);
    
    // 添加规则
    if (groupData.rules && groupData.rules.length > 0) {
        groupData.rules.forEach(rule => {
            const conditionId = generateId('cond_');
            const $condition = $(createFilterConditionHTML(conditionId, groupLevel + 1)).appendTo($conditionsContainer);
            
            // 设置字段
            $condition.find('.filter-field').val(rule.field).trigger('change');
            
            // 设置操作符（在字段change触发后操作符会已经更新）
            setTimeout(() => {
                $condition.find('.filter-operator').val(rule.operator).trigger('change');
                
                // 设置值（在操作符change触发后值输入框会已经更新）
                setTimeout(() => {
                    const $valueInput = $condition.find('.filter-value');
                    if ($valueInput.length > 0 && rule.value !== null) {
                        $valueInput.val(rule.value);
                    }
                }, 10);
            }, 10);
        });
    }
    
    // 添加子组
    if (groupData.groups && groupData.groups.length > 0) {
        groupData.groups.forEach(childGroupData => {
            const childGroupId = generateId('group_');
            const $childGroup = $(createFilterGroupHTML(childGroupId, groupLevel + 1, childGroupData.operator))
                .appendTo($conditionsContainer);
                
            // 设置组操作符
            $childGroup.attr('data-operator', childGroupData.operator);
            $childGroup.find(`.btn-logic[data-logic="${childGroupData.operator}"]`)
                .addClass('btn-primary').removeClass('btn-outline-primary')
                .siblings('.btn-logic')
                .removeClass('btn-primary').addClass('btn-outline-primary');
                
            // 递归处理子组
            rebuildGroupContent($childGroup, childGroupData);
        });
    }
}

/**
 * Opens the generic CRUD modal.
 * @param {string} mode - 'add' or 'edit'.
 * @param {string|number|null} [itemId=null] - The ID of the item to edit (only for 'edit' mode).
 * @param {object|null} [config=null] - The full moduleConfig object.
 */
async function openModal(mode, itemId = null, config = null) {
    if (!crudModal) {
        console.error("CRUD Modal instance is not initialized.");
        return;
    }

    const modalTitleElement = document.getElementById('genericCrudModalLabel');
    const itemIdElement = document.getElementById('itemId'); // Hidden input in the modal form
    const formContainer = document.getElementById('dynamic-form-fields'); // Get container

    // Basic validation
    if (!modalTitleElement || !itemIdElement || !formContainer) {
        console.error("Modal essential elements (title, itemId, formContainer) not found.");
        showToast("模态框组件错误", "error");
        return;
    }
    if (!config || !config.fields || !config.module_name) {
         console.error("Modal configuration (config, fields, module_name) is missing.");
         showToast("模态框配置错误", "error");
         return;
    }

    // Reset common elements
    itemIdElement.value = itemId || '';
    formContainer.innerHTML = ''; // Clear previous content
    // --- TODO: Reset form validation state here ---

    // --- Handle based on mode ---
    if (mode === 'add') {
        modalTitleElement.textContent = '添加新记录';
        try {
             // Generate form immediately for add mode
             generateFormFields(config.fields, formContainer, 'add', null); // No initial data for add
             crudModal.show();
        } catch (genError) {
             console.error("Error generating form for add mode:", genError);
             showToast(`生成表单时出错: ${genError.message}`, 'error');
             formContainer.innerHTML = `<div class="alert alert-danger">生成表单时出错: ${genError.message}</div>`;
             crudModal.show(); // Show modal even with error to display message
        }

    } else if (mode === 'edit') {
        modalTitleElement.textContent = '编辑记录';
        if (!itemId) {
            console.error("Item ID is required for edit mode.");
            showToast("错误：无法编辑记录，缺少 ID。", "error");
            return; // Don't open modal if ID is missing for edit
        }

        // Show loading indicator and the modal *before* fetching data
        formContainer.innerHTML = '<div class="d-flex justify-content-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
        crudModal.show();

        try {
            console.log(`Fetching data for edit: ${config.module_name}, ID: ${itemId}`);
            const itemData = await fetchItemData(config.module_name, itemId);
            console.log("Fetched item data:", itemData);

            // Ensure container still exists (user might close modal quickly)
            const currentFormContainer = document.getElementById('dynamic-form-fields');
            if (!currentFormContainer) {
                console.warn("Modal form container no longer exists after data fetch.");
                return; // Exit if modal was closed
            }
            
            // Generate form fields and populate with fetched data
            generateFormFields(config.fields, currentFormContainer, 'edit', itemData);

        } catch (error) {
            console.error("Error during edit mode data fetch or form generation:", error);
             showToast(`加载编辑数据失败: ${error.message}`, 'error');
             
             // Show error in the modal body if it still exists
             const errorFormContainer = document.getElementById('dynamic-form-fields');
             if (errorFormContainer) {
                errorFormContainer.innerHTML = `<div class="alert alert-danger">无法加载数据: ${error.message}</div>`;
             } // If modal closed, error already shown in toast
        }

    } else {
        console.error('Invalid modal mode:', mode);
        showToast(`无效的操作模式: ${mode}`, 'error');
    }
}

/**
 * Initializes the CRUD modal instance and sets up event listeners for add/edit buttons.
 *       console.warn("No filter fields configured. Advanced filter UI will be empty.");
 *        $topLevelGroupsContainer.html('<p class="text-muted">此模块未配置高级筛选字段。</p>');
 *        // Optionally disable related buttons
 *        $('#btnApplyAdvancedFilter, #btnResetAdvancedFilter, #btnTestFilter, #btnAddTopLevelGroup').prop('disabled', true);
*    }
*
*    // --- TODO LATER: Bind events for interactions (add/remove/toggle logic) ---
*    // bindAdvancedFilterInteractionEvents();
*/
