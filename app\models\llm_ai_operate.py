from datetime import datetime
from app import db

class LlmAiOperate(db.Model):
    """AI模型操作表ORM模型"""
    __tablename__ = 'llm_ai_operate'

    id = db.Column(db.Integer, primary_key=True, comment='自增主键')
    model_name = db.Column(db.String(120), nullable=False, comment='Model 调用名称')
    sort_order = db.Column(db.Integer, default=1, comment='排序顺序')
    need_sync = db.Column(db.Integer, default=0, comment='1 表示当前需要进行数据同步; 0或Null或空表示不需要进行同步')
    create_time = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = db.Column(db.String(50), nullable=True, comment='创建人')
    update_by = db.Column(db.String(50), nullable=True, comment='更新人')
    is_deleted = db.Column(db.Integer, default=0, comment='逻辑删除标志')
    
    def to_dict(self):
        """转换为字典表示"""
        return {
            'id': self.id,
            'model_name': self.model_name,
            'sort_order': self.sort_order,
            'need_sync': self.need_sync,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None,
            'create_by': self.create_by,
            'update_by': self.update_by,
            'is_deleted': self.is_deleted
        }