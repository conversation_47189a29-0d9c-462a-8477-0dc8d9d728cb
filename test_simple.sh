#!/bin/bash

# 简化的测试脚本，用于验证API响应打印功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印API测试详情到控制台
print_api_response() {
    local key="$1"
    local http_code="$2"
    local response="$3"
    local result="$4"
    
    echo ""
    echo "    ${BLUE}━━━ API响应详情 ━━━${NC}"
    echo "    ${BLUE}密钥:${NC} ${key:0:20}***"
    echo "    ${BLUE}HTTP状态码:${NC} $http_code"
    echo "    ${BLUE}验证结果:${NC} $result"
    
    if [ -n "$response" ]; then
        echo "    ${BLUE}API响应内容:${NC}"
        # 格式化JSON响应以便阅读
        if echo "$response" | grep -q '^{'; then
            echo "$response" | sed 's/,/,\n        /g' | sed 's/{/{\n        /g' | sed 's/}/\n    }/g'
        else
            echo "        $response"
        fi
    fi
    echo "    ${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
}

# 模拟API测试
echo "=== OpenAI API密钥验证测试演示 ==="
echo ""

# 模拟真实密钥的成功响应
echo "${GREEN}[  1/  3] 验证密钥 sk-svcacct--6QW*** ... ✓ 有效${NC}"
print_api_response "***********************************************************************************************************************************************************************" "200" '{"object":"list","data":[{"id":"gpt-4","object":"model","created":1687882411,"owned_by":"openai"},{"id":"gpt-3.5-turbo","object":"model","created":1677610602,"owned_by":"openai"},{"id":"text-davinci-003","object":"model","created":1669599635,"owned_by":"openai-internal"}]}' "VALID"

echo ""
echo "${RED}[  2/  3] 验证密钥 sk-123456789012*** ... ✗ 无效 [HTTP 401]${NC}"
print_api_response "sk-1234567890123456789012" "401" '{"error":{"message":"Invalid API key provided","type":"invalid_request_error","param":null,"code":"invalid_api_key"}}' "HTTP_ERROR"

echo ""
echo "${RED}[  3/  3] 验证密钥 sk-987654321098*** ... ✗ 网络错误 (curl: 6)${NC}"
print_api_response "sk-9876543210987654321098" "" "Could not resolve host" "CURL_ERROR"

echo ""
echo "=== 测试演示完成 ==="
