from app.services.business.base_service import BaseService
from app.dao.llm_ai_data_dao import LlmAiDataDAO

class LlmAiDataService(BaseService):
    """
    Business logic service for the LlmAiData model.
    Inherits generic service methods and initializes with LlmAiDataDAO.
    """
    def __init__(self):
        """
        Initialize the service with an instance of LlmAiDataDAO.
        """
        llm_ai_data_dao = LlmAiDataDAO()
        super().__init__(llm_ai_data_dao)

    # Add LlmAiData specific business logic methods here if needed in the future. 