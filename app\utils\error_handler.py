import logging
from flask import jsonify
from app.utils.exceptions import AppException, APIException, ValidationException

# 配置日志
logger = logging.getLogger(__name__)

def register_error_handlers(app):
    """注册全局错误处理器"""
    
    @app.errorhandler(AppException)
    def handle_app_exception(e):
        """处理应用异常"""
        return jsonify(e.to_dict()), e.status_code
    
    @app.errorhandler(404)
    def handle_not_found(e):
        """处理404错误"""
        return jsonify({
            'message': '请求的资源不存在',
            'status_code': 404
        }), 404
    
    @app.errorhandler(500)
    def handle_server_error(e):
        """处理500错误"""
        logger.error(f"Server error: {str(e)}")
        
        return jsonify({
            'message': '服务器内部错误',
            'status_code': 500
        }), 500
    
    @app.errorhandler(Exception)
    def handle_unexpected_error(e):
        """处理未预期的异常"""
        logger.error(f"Unexpected error: {str(e)}")
        
        if app.config['DEBUG']:
            # 在开发环境下返回详细错误
            return jsonify({
                'message': f'未预期的错误: {str(e)}',
                'status_code': 500
            }), 500
        else:
            # 在生产环境下返回简单错误
            return jsonify({
                'message': '服务器内部错误',
                'status_code': 500
            }), 500