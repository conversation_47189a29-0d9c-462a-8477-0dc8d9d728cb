#!/bin/bash

# 简单的测试脚本来验证基本功能
echo "开始测试脚本功能..."

# 测试1: 检查脚本是否能正确显示帮助信息
echo "=== 测试1: 显示帮助信息 ==="
bash testPy/check.sh 2>&1 | head -5

echo ""
echo "=== 测试2: 检查参数解析 ==="
# 测试2: 使用测试密钥文件，但不进行实际API调用
echo "aaa-test123456789012345678" > temp_test_key.txt
echo "测试密钥文件内容:"
cat temp_test_key.txt

echo ""
echo "=== 测试3: 运行脚本（前几步） ==="
# 使用timeout来限制执行时间，避免实际API调用
timeout 5 bash testPy/check.sh temp_test_key.txt --prefix no_check --length 0 --api-url "https://httpbin.org/get" --fail-keyword "error" 2>&1 | head -10

# 清理
rm -f temp_test_key.txt

echo ""
echo "测试完成！"
