import requests
from app.utils.exceptions import APIException, ValidationException

import json
import pymysql
import pymysql.cursors

import os
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量
load_dotenv()

# --- 数据库配置 ---
# !!! 重要: 请将以下信息更新为您的 MySQL 数据库凭据。
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),      # MySQL 服务器 IP/主机名
    'user': os.getenv('DB_USER', 'root'),           # MySQL 用户名
    'password': os.getenv('DB_PASSWORD', ''),       # MySQL 密码
    'db': os.getenv('DB_NAME', 'test'),             # 数据库名称
    'charset': 'utf8mb4',                           # 字符集
    'cursorclass': pymysql.cursors.DictCursor       # 可选: 使查询结果表现得像字典
}

class ServiceOpenAI:
    """OpenAI API服务类"""
    
    def __init__(self, timeout=30):
        """初始化"""
        self.timeout = timeout
    
    def get_models(self, api_base, api_key):
        """获取模型列表"""
        # 参数验证
        if not api_base:
            raise ValidationException({"api_base": ["API基础URL不能为空"]})
        if not api_key:
            raise ValidationException({"api_key": ["API密钥不能为空"]})
            
        # 规范化API基础URL
        if not api_base.endswith('/'):
            api_base += '/'
            
        # 构建请求URL
        url = f"{api_base}"
        
        # 构建请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            # 发送请求
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            data = response.json()
            
            # 检查响应格式
            if not isinstance(data, dict) or 'data' not in data:
                raise APIException("API响应格式错误", status_code=500)
                
            # 提取模型列表
            return data.get('data', [])
        except Exception as e:
            raise APIException(f"获取模型列表失败: {str(e)}", status_code=500)
    
    def extract_field_values(self, models, field_name):
        """从模型列表中提取指定字段的值"""
        if not field_name:
            raise ValidationException({"field_name": ["字段名称不能为空"]})
            
        result = []
        
        for model in models:
            # 处理嵌套字段
            value = self._get_nested_value(model, field_name)
            if value is not None:
                result.append(value)
                
        return result
    
    def _get_nested_value(self, data, field_path):
        """获取嵌套字段值"""
        if not field_path:
            return None
            
        # 处理字段路径
        parts = field_path.split('.')
        value = data
        
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return None
                
        return value
    


    ## 以下代码为 对接筛选数据入库操作
    def _find_values_recursive_visual_layer(self, current_data, current_visual_depth, target_visual_depth, field_name, results_set):
        """
        根据"视觉层级"递归查找目标字段的值。
        视觉层级定义：
        - 层级1：最外层对象的键，或最外层数组中字典元素的键。
        - 层级N：是层级(N-1)的有效值（该值必须是字典，或者是列表其元素为字典）内部的键。
        - 列表本身不增加视觉层级数。层级是针对"包含目标字段的字典"的嵌套深度而言的。

        参数:
        current_data (any): 当前正在处理的 Python 对象（字典、列表、简单值）。
        current_visual_depth (int): 当前正在考察的"潜在字典容器"的视觉层级。
                                    初始调用时，对于根JSON的直接内容，此值为1。
        target_visual_depth (int): 用户期望找到 field_name 的视觉层级。
        field_name (str): 目标字段名。
        results_set (set): 存储提取到的唯一字符串化值的集合。
        """
        if current_data is None:
            return

        # 如果当前视觉层级已超过目标层级，则无需继续深入此路径
        if current_visual_depth > target_visual_depth:
            return

        if isinstance(current_data, dict):
            # 当前 current_data 是一个字典，它正位于 current_visual_depth
            if current_visual_depth == target_visual_depth:
                # 此字典位于目标视觉层级，检查它是否包含目标字段
                if field_name in current_data and current_data[field_name] is not None:
                    value_str = str(current_data[field_name])
                    print(f"  [调试信息] 捕获于字典 (视觉层级 {current_visual_depth}): 键='{field_name}', 值='{value_str}'")
                    results_set.add(value_str)
            
            # 无论当前字典是否为目标层级字典，都需要遍历其值，这些值将构成下一视觉层级的内容
            # （如果当前字典层级已达目标层级，其子项就无需检查是否包含目标字段了，因为目标字段只在目标层级的字典中找，
            # 但子项仍需被遍历，以防子项是列表，列表内又有目标层级的字典）
            # 因此，只要 current_visual_depth 未超过 target_visual_depth，就继续探索其子节点
            # 子节点（字典的值）的视觉层级将是 current_visual_depth + 1
            for key, value_node in current_data.items():
                self._find_values_recursive_visual_layer(value_node, current_visual_depth + 1, target_visual_depth, field_name, results_set)

        elif isinstance(current_data, list):
            # 当前 current_data 是一个列表。列表本身不计为一个独立的视觉层级。
            # 我们将遍历其元素，并使用相同的 current_visual_depth 来处理这些元素。
            # 如果元素是字典，该字典将被视为在 current_visual_depth 进行评估。
            # 如果元素是列表，将进一步递归。
            for element_node in current_data:
                self._find_values_recursive_visual_layer(element_node, current_visual_depth, target_visual_depth, field_name, results_set)
        
        # 其他简单类型（字符串、数字等）不能作为容器，故不作处理。

    def batch_save_model_names(self, model_names_set):
        """
        将一组模型名称保存到 'llm_ai_operate' 表中。
        在Python应用层面进行去重：先查询数据库中已存在的值，然后只插入新值。
        保留数据库层面的 `INSERT IGNORE` 作为并发写入等情况的最后防线。
        返回实际插入的新记录数量。
        """
        if not model_names_set:
            print("[数据库操作] 没有提取到模型名称需要保存。")
            return 0

        print(f"\n[数据库操作] 提取到 {len(model_names_set)} 个潜在的模型名称，将进行去重处理。")
        
        connection = pymysql.connect(**DB_CONFIG)
        if not connection:
            return 0 # 连接失败则无法继续

        existing_names_in_db = set()
        try:
            # 1. 查询数据库中已存在的值
            # 为了避免SQL IN子句过长或参数过多的问题，如果集合很大，理论上应分批查询
            # 但这里假设 model_names_set 通常不会极端大
            if model_names_set: # 只有当有待检查的值时才查询
                with connection.cursor() as cursor:
                    # 使用 %s 作为占位符，pymysql 会处理转义
                    # 将 set 转换为 tuple 传递给 IN 子句
                    placeholders = ', '.join(['%s'] * len(model_names_set))
                    sql_check_existence = f"SELECT model_name FROM llm_ai_operate WHERE is_deleted=0 AND model_name IN ({placeholders})"
                    cursor.execute(sql_check_existence, tuple(model_names_set))
                    rows = cursor.fetchall()
                    for row in rows:
                        existing_names_in_db.add(row['model_name']) # 假设cursorclass=DictCursor
                
                if existing_names_in_db:
                    print(f"[数据库操作] 在数据库中找到了以下 {len(existing_names_in_db)} 个已存在的名称 (将不会重复插入):")
                    for name in sorted(list(existing_names_in_db)):
                        print(f"  - 已存在: \"{name}\"")
                else:
                    print("[数据库操作] 待检查的名称在数据库中均未找到。")

            # 2. 从待保存集合中剔除已存在的值，得到真正需要插入的新值
            new_names_to_insert_set = model_names_set - existing_names_in_db
            
            if not new_names_to_insert_set:
                print("[数据库操作] 应用层去重后，没有新的模型名称需要插入数据库。")
                return 0

            data_to_insert_tuples = [(name,) for name in sorted(list(new_names_to_insert_set))]
            print(f"[数据库操作] 应用层去重后，准备批量插入以下 {len(data_to_insert_tuples)} 个新的模型名称到数据库:")
            for item_tuple in data_to_insert_tuples:
                print(f"  - 新增待插: \"{item_tuple[0]}\"")

            # 3. 批量插入新值 (仍然使用 INSERT IGNORE 作为最后保障)
            saved_count = 0
            with connection.cursor() as cursor:
                sql_insert = "INSERT IGNORE INTO llm_ai_operate (model_name) VALUES (%s)"
                affected_rows = cursor.executemany(sql_insert, data_to_insert_tuples)
                connection.commit()
                saved_count = affected_rows if affected_rows is not None else 0
                
                if saved_count > 0:
                    print(f"[数据库操作] 成功向数据库中插入 {saved_count} 个新的模型名称。")
                else:
                    # 如果 affected_rows 是0，但 new_names_to_insert_set 非空，
                    # 可能是因为 INSERT IGNORE 由于某些原因（如并发写入后又存在了）再次跳过了。
                    print(f"[数据库操作] 尝试插入 {len(data_to_insert_tuples)} 个新识别的名称，但实际插入了 {saved_count} 行。若前者大于0，请检查并发或数据库状态。")
            return saved_count

        except pymysql.Error as e:
            print(f"数据库操作期间出错: {e}")
            if connection:
                try: connection.rollback() 
                except pymysql.Error as re: print(f"回滚期间出错: {re}")
            return 0 # 返回0表示没有成功保存新记录
        except Exception as ex:
            print(f"数据库操作期间发生意外错误: {ex}")
            if connection:
                try: connection.rollback()
                except pymysql.Error as rex: print(f"回滚期间出错: {rex}")
            return 0
        finally:
            if connection:
                connection.close()

    def process_json_and_save_to_db(self, json_string_input, target_field_key, target_layer):
        """
        主函数，用于解析 JSON 字符串，从目标"视觉层级"提取指定字段值，
        打印它们，并将唯一的、数据库中尚不存在的新值批量保存到数据库。
        """
        result = {'total':0,'imported':0,'existed':0}
        print(f"开始处理JSON: 目标视觉层级 {target_layer}, 目标字段 '{target_field_key}'...")
        if not isinstance(json_string_input, str):
            print(f"输入错误: parse_json_string 需要一个字符串，但得到了 {type(json_string_input)}")
            return result
        
        cleaned_json_string = json_string_input.strip() # 去除前后空白
        parsed_data = None
        try:
            parsed_data = json.loads(cleaned_json_string)
            if isinstance(parsed_data, (dict, list)):
                pass
            else:
                print(f"警告: JSON 根不是对象或数组, 而是: {type(parsed_data)}。此函数期望 JSON 对象或数组。")
                parsed_data = None
        except json.JSONDecodeError as e:
            print(f"解码 JSON 时出错: {e} (原始字符串片段: '{cleaned_json_string[:100]}...')")
            parsed_data = None
        except Exception as e:
            print(f"JSON 解析期间发生意外错误: {e}")
            parsed_data = None
        if parsed_data is None:
            print("由于 JSON 解析问题，处理中止。")
            print("=" * 50 + "\n")
            return result

        extracted_names = set()
        if parsed_data is None:
            print("无法从 None 中提取值 (parsed_json_data 为 null)。")
        else:
            self._find_values_recursive_visual_layer(parsed_data, 1, int(target_layer.strip()), target_field_key, extracted_names)    
        
        if not extracted_names:
            print(f'未能根据"视觉层级" {target_layer} 和字段名 {target_field_key} 找到任何值。')
        else:
            print(f'根据"视觉层级"提取到 {len(extracted_names)} 个唯一的模型名称值:')
            for name_idx, name_val in enumerate(sorted(list(extracted_names))):
                print(f'  提取值 #{name_idx + 1}: "{name_val}"')
            # 详细的待保存列表打印已移至 batch_save_model_names 内部
            print("-" * 30)

        num_saved = self.batch_save_model_names(extracted_names)
        print(f'数据库保存操作完成。共向数据库成功插入了 {num_saved} 条新的记录。详情请查看以上日志。')
        print('=' * 50 + '\n')
        
        return {
            'total':    len(extracted_names),
            'imported': num_saved,
            'existed':  len(extracted_names) - num_saved
        }