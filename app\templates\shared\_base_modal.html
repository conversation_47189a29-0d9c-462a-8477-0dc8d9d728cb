<!-- Generic CRUD Modal -->
<div class="modal fade" id="genericCrudModal" tabindex="-1" aria-labelledby="genericCrudModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg"> <!-- 可根据需要调整大小: modal-sm, modal-lg, modal-xl -->
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="genericCrudModalLabel">Modal title</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                 <form id="genericCrudForm">
                     <!-- 表单字段将由 JS 动态生成于此 -->
                     <input type="hidden" id="csrf_token" name="csrf_token" value="{{ csrf_token() }}"> <!-- 添加 CSRF Token -->
                     <input type="hidden" id="itemId" name="itemId" value=""> <!-- 用于存储编辑项的 ID -->
                     <div id="dynamic-form-fields"></div> <!-- Container for dynamically generated fields -->
                 </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btnModalClose" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary btnModalSave">保存</button>
            </div>
        </div>
    </div>
</div> 