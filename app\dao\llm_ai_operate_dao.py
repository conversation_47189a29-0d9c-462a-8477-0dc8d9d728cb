from app import db
from app.models.llm_ai_operate import Llm<PERSON><PERSON><PERSON>perate
from sqlalchemy.exc import IntegrityError
from flask import current_app
import sqlalchemy.sql.expression as sql
from sqlalchemy import and_, or_, not_
import datetime
from sqlalchemy import text # For sql.false() or similar
import decimal # For Numeric types

class LlmAiOperateDAO:
    """LlmAiOperate模型的数据访问对象类"""
    
    def __init__(self):
        """初始化"""
        self.model_class = LlmAiOperate
    
    def get_by_id(self, id):
        """根据ID获取记录"""
        return LlmAiOperate.query.filter_by(id=id).first()
    
    def get_by_model_name(self, model_name):
        """根据模型名称获取记录"""
        return LlmAiOperate.query.filter_by(model_name=model_name, is_deleted=0).first()
    
    def get_all(self, page=1, per_page=20, sort_by='sort_order', order='asc', search_value=None, advanced_filter=None, **filters):
        """获取所有记录，支持分页、排序和筛选"""
        # 获取查询对象
        query = self.get_all_query(
            sort_by=sort_by,
            order=order,
            search_value=search_value,
            advanced_filter=advanced_filter,
            **filters
        )
        
        # 记录最终的SQL查询（包含分页）
        try:
            # 获取分页前的完整SQL查询
            sql_stmt = str(query.statement.compile(
                compile_kwargs={"literal_binds": True}
            ))
            current_app.logger.info(f"Final paginated SQL query: SELECT * FROM ({sql_stmt}) LIMIT {per_page} OFFSET {(page-1)*per_page}")
        except Exception as e:
            current_app.logger.error(f"Error logging final SQL query: {e}")
            
        # 应用分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        return pagination
    
    def create(self, data):
        """创建新记录"""
        try:
            model = LlmAiOperate(**data)
            db.session.add(model)
            db.session.commit()
            return model
        except IntegrityError:
            db.session.rollback()
            return None
    
    def update(self, id, data):
        """更新记录"""
        model = self.get_by_id(id)
        if model:
            for key, value in data.items():
                if hasattr(model, key):
                    setattr(model, key, value)
            db.session.commit()
            return model
        return None
    
    def delete(self, id):
        """逻辑删除记录"""
        model = self.get_by_id(id)
        if model:
            model.is_deleted = 1
            db.session.commit()
            return True
        return False
    
    def bulk_delete(self, ids):
        """批量逻辑删除记录"""
        if not ids:
            return 0
            
        count = LlmAiOperate.query.filter(
            LlmAiOperate.id.in_(ids),
            LlmAiOperate.is_deleted == 0
        ).update(
            {LlmAiOperate.is_deleted: 1},
            synchronize_session=False
        )
        db.session.commit()
        return count
    
    def update_sort_order(self, id, action):
        """更新排序顺序，支持置顶、置底"""
        try:
            model = self.get_by_id(id)
            if not model:
                return False
                
            if action == 'top':
                # 获取最小排序值
                min_order = db.session.query(db.func.min(LlmAiOperate.sort_order)).filter(
                    LlmAiOperate.is_deleted == 0
                ).scalar() or 0
                
                # 如果当前记录已经是最小的排序值或者比最小值还小，则不需要改变
                if model.sort_order is not None and model.sort_order <= min_order:
                    return True
                
                # 使用间隔策略设置新的排序值，留出空间以减少未来更新次数
                new_order = min_order - 10
                model.sort_order = new_order
            elif action == 'bottom':
                # 获取最大排序值
                max_order = db.session.query(db.func.max(LlmAiOperate.sort_order)).filter(
                    LlmAiOperate.is_deleted == 0
                ).scalar() or 0
                
                # 如果当前记录已经是最大的排序值或者比最大值还大，则不需要改变
                if model.sort_order is not None and model.sort_order >= max_order:
                    return True
                
                # 使用间隔策略设置新的排序值，留出空间以减少未来更新次数
                new_order = max_order + 10
                model.sort_order = new_order
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            return False

    def batch_update_sort_order(self, ids, action):
        """批量更新排序顺序，支持置顶、置底"""
        if not ids:
            return 0
            
        try:
            count = 0
            # 获取现有记录
            models = LlmAiOperate.query.filter(
                LlmAiOperate.id.in_(ids),
                LlmAiOperate.is_deleted == 0
            ).all()
            
            if not models:
                return 0
                
            if action == 'top':
                # 获取最小排序值
                min_order = db.session.query(db.func.min(LlmAiOperate.sort_order)).filter(
                    LlmAiOperate.is_deleted == 0
                ).scalar() or 0
                
                # 给每条记录一个从小到大的排序值，最上面的排序值最小
                # 按照当前排序值排序，保持相对顺序
                sorted_models = sorted(models, key=lambda m: m.sort_order if m.sort_order is not None else float('inf'))
                for i, model in enumerate(sorted_models):
                    model.sort_order = min_order - (len(sorted_models) - i) * 10
                    count += 1
                    
            elif action == 'bottom':
                # 获取最大排序值
                max_order = db.session.query(db.func.max(LlmAiOperate.sort_order)).filter(
                    LlmAiOperate.is_deleted == 0
                ).scalar() or 0
                
                # 给每条记录一个从小到大的排序值，最下面的排序值最大
                # 按照当前排序值排序，保持相对顺序
                sorted_models = sorted(models, key=lambda m: m.sort_order if m.sort_order is not None else float('-inf'))
                for i, model in enumerate(sorted_models):
                    model.sort_order = max_order + (i + 1) * 10
                    count += 1
            
            db.session.commit()
            
            # 如果排序值间隔变得太小或太大，可以考虑重新整理所有排序值
            self._maybe_normalize_sort_order()
            
            return count
        except Exception as e:
            db.session.rollback()
            return 0
            
    def _maybe_normalize_sort_order(self):
        """检查并可能重新规范化所有排序值，保持间隔合理"""
        try:
            # 获取所有非删除记录数量
            total_count = LlmAiOperate.query.filter_by(is_deleted=0).count()
            
            # 如果记录较多且排序值范围过大，则重新规范化
            min_order = db.session.query(db.func.min(LlmAiOperate.sort_order)).filter(
                LlmAiOperate.is_deleted == 0
            ).scalar() or 0
            
            max_order = db.session.query(db.func.max(LlmAiOperate.sort_order)).filter(
                LlmAiOperate.is_deleted == 0
            ).scalar() or 0
            
            # 如果排序值范围超过记录数的100倍，则重新规范化
            if (max_order - min_order) > total_count * 100:
                # 获取所有记录并按排序值排序
                models = LlmAiOperate.query.filter_by(is_deleted=0).order_by(LlmAiOperate.sort_order.asc()).all()
                
                # 重新分配排序值，保持顺序但使用合理间隔
                for i, model in enumerate(models):
                    model.sort_order = i * 10
                
                db.session.commit()
                
        except Exception:
            db.session.rollback()
    
    def update_sync_status(self, id, need_sync=1):
        """更新同步状态"""
        model = self.get_by_id(id)
        if model:
            model.need_sync = need_sync
            db.session.commit()
            return True
        return False
        
    def bulk_update_sync_status(self, ids, need_sync=1):
        """批量更新同步状态"""
        if not ids:
            return 0
            
        count = LlmAiOperate.query.filter(
            LlmAiOperate.id.in_(ids),
            LlmAiOperate.is_deleted == 0
        ).update(
            {LlmAiOperate.need_sync: need_sync},
            synchronize_session=False
        )
        db.session.commit()
        return count
    
    def get_all_query(self, sort_by='id', order='asc', search_value=None, advanced_filter=None, **filters):
        """构建查询对象，支持排序、搜索和筛选"""
        # 开始基本查询
        query = self.model_class.query.filter_by(**filters)
        
        # 支持按删除状态筛选
        if 'is_deleted' not in filters:
            query = query.filter(self.model_class.is_deleted == 0)
        
        # 支持搜索
        if search_value:
            search_filter = self.model_class.model_name.like(f'%{search_value}%')
            query = query.filter(search_filter)
        
        # 支持高级筛选
        if advanced_filter and isinstance(advanced_filter, dict):
            query = self._apply_advanced_filter(query, advanced_filter)
        
        # 支持排序
        if hasattr(self.model_class, sort_by):
            sort_column = getattr(self.model_class, sort_by)
            if order.lower() == 'desc':
                sort_column = sort_column.desc()
            query = query.order_by(sort_column)
        
        # 记录SQL查询语句
        try:
            # 获取原始SQL语句和参数
            sql_stmt = str(query.statement.compile(
                compile_kwargs={"literal_binds": True}
            ))
            current_app.logger.info(f"Generated SQL query: {sql_stmt}")
        except Exception as e:
            current_app.logger.error(f"Error logging SQL query: {e}")
        
        return query
        
    def _build_filter_condition(self, filter_data):
        """从JSON数据构建筛选条件"""
        current_app.logger.debug(f"[_build_filter_condition] Received filter_data: {filter_data}")
        if not filter_data or not isinstance(filter_data, dict):
            return None
        
        filter_type = filter_data.get('type')

        # 处理组 (重构部分)
        if filter_type == 'group':
            current_app.logger.debug(f"[_build_filter_condition] Processing group...")
            conditions = []
            # 获取组内条件的逻辑操作符 (AND 或 OR)
            group_operator = filter_data.get('operator', 'AND').upper()
            if group_operator not in ['AND', 'OR']:
                current_app.logger.warning(f"Invalid group operator '{group_operator}' in filter group, defaulting to 'AND'. Filter data: {filter_data}")
                group_operator = 'AND'
            current_app.logger.debug(f"[_build_filter_condition] Group operator: {group_operator}")
            
            # 递归处理组内的所有条件或嵌套组
            for item in filter_data.get('conditions', []):
                current_app.logger.debug(f"[_build_filter_condition]  Recursively processing item: {item}")
                condition = self._build_filter_condition(item) # 递归调用
                if condition is not None:
                    current_app.logger.debug(f"[_build_filter_condition]  Recursion returned condition: {condition}")
                    conditions.append(condition)
                else:
                    current_app.logger.debug(f"[_build_filter_condition]  Recursion returned None for item: {item}")
            
            # 如果没有有效的子条件，则此组无效
            if not conditions:
                current_app.logger.debug(f"[_build_filter_condition] No valid conditions found in group, returning None.")
                return None
            
            # 使用组的操作符组合所有子条件
            final_condition = None
            if group_operator == 'AND':
                # SQLAlchemy and_() 会自动处理括号: (cond1 AND cond2 AND ...)
                final_condition = and_(*conditions)
            else: # OR
                # SQLAlchemy or_() 会自动处理括号: (cond1 OR cond2 OR ...)
                final_condition = or_(*conditions)
            
            current_app.logger.debug(f"[_build_filter_condition] Returning combined group condition: {final_condition}")
            return final_condition
        
        # 处理条件 (保持现有逻辑，但需仔细检查类型转换)
        elif filter_type == 'condition':
            current_app.logger.debug(f"[_build_filter_condition] Processing condition...")
            field_name = filter_data.get('field')
            operator = filter_data.get('operator')
            value = filter_data.get('value')
            current_app.logger.debug(f"[_build_filter_condition] Condition details: Field={field_name}, Op={operator}, Value={value}")
            
            # 验证字段存在
            if not hasattr(self.model_class, field_name):
                current_app.logger.error(f"Advanced filter error: Field '{field_name}' not found on model {self.model_class.__name__}.")
                return None # 或者抛出异常
            
            # 获取字段属性
            field = getattr(self.model_class, field_name)
            
            # 获取字段类型 (确保导入 sqlalchemy.types)
            from sqlalchemy.types import Integer, Float, Double, Numeric, String, Boolean, DateTime, Date, Time # 添加 Time
            try:
                field_type = field.property.columns[0].type
            except IndexError:
                current_app.logger.error(f"Could not determine type for field '{field_name}'.")
                return sql.false() # Treat as non-match if type unknown
            except AttributeError:
                current_app.logger.error(f"Field '{field_name}' does not seem to be a standard column property.")
                return sql.false() # Treat as non-match

            # --- 重构值处理和类型转换逻辑 ---

            processed_value = None
            input_value = value # Keep original for logging/debugging

            # 1. 处理特殊操作符 is_null / is_not_null (不关心 value)
            if operator == 'is_null':
                return field.is_(None)
            if operator == 'is_not_null':
                return field.isnot(None)

            # 2. 处理 value 为 None 或 空字符串 的情况 (对于其他操作符)
            is_value_empty = (input_value is None or str(input_value).strip() == '')

            if is_value_empty:
                if operator == 'eq':
                    # 'field eq null/empty' -> field IS NULL
                    return field.is_(None)
                elif operator == 'neq':
                    # 'field neq null/empty' -> field IS NOT NULL
                    return field.isnot(None)
                else:
                    # 对于 >, <, contains 等操作符，与 null/empty 比较通常无意义或为 false
                    current_app.logger.debug(f"Operator '{operator}' with empty/null value for field '{field_name}' results in false condition.")
                    return sql.false() # Return a condition that is always false

            # 3. value 非空，进行类型转换 (包裹在 try-except 中)
            try:
                if isinstance(field_type, Boolean):
                    if isinstance(input_value, str):
                        processed_value = input_value.lower() in ('true', '1', 'yes', 'y')
                    else:
                        processed_value = bool(input_value)
                    current_app.logger.debug(f"Converted '{input_value}' to Boolean: {processed_value}")
                elif isinstance(field_type, DateTime):
                    if isinstance(input_value, str):
                        try:
                            # 尝试解析 ISO 8601 格式 (可能带 T, Z, 时区)
                            processed_value = datetime.datetime.fromisoformat(input_value.replace('Z', '+00:00'))
                            current_app.logger.debug(f"Parsed ISO DateTime string '{input_value}' to: {processed_value}")
                        except ValueError:
                            try:
                                # 尝试解析 YYYY-MM-DD HH:MM:SS 格式
                                processed_value = datetime.datetime.strptime(input_value, '%Y-%m-%d %H:%M:%S')
                                current_app.logger.debug(f"Parsed 'YYYY-MM-DD HH:MM:SS' DateTime string '{input_value}' to: {processed_value}")
                            except ValueError:
                                try:
                                     # 尝试仅解析日期部分 (可能来自 date input)，补全时间为 00:00:00
                                     date_part = datetime.date.fromisoformat(input_value)
                                     processed_value = datetime.datetime.combine(date_part, datetime.time.min)
                                     current_app.logger.debug(f"Parsed Date string '{input_value}' and combined to DateTime: {processed_value}")
                                except ValueError:
                                     current_app.logger.error(f"Could not parse DateTime string: '{input_value}' for field '{field_name}'.")
                                     return sql.false() # Fail condition if parse fails
                    elif isinstance(input_value, datetime.datetime):
                        processed_value = input_value # 已经是正确类型
                    elif isinstance(input_value, datetime.date): # 如果输入是 date 但列是 datetime
                         processed_value = datetime.datetime.combine(input_value, datetime.time.min)
                    else:
                         current_app.logger.error(f"Unsupported input type for DateTime field '{field_name}': {type(input_value)}")
                         return sql.false()
                elif isinstance(field_type, Date):
                     if isinstance(input_value, str):
                        try:
                            # 尝试解析日期部分 (可能来自 datetime 或 date input)
                            date_part_str = input_value.split('T')[0]
                            processed_value = datetime.date.fromisoformat(date_part_str)
                            current_app.logger.debug(f"Parsed Date string '{input_value}' to: {processed_value}")
                        except ValueError:
                            current_app.logger.error(f"Could not parse Date string: '{input_value}' for field '{field_name}'.")
                            return sql.false()
                     elif isinstance(input_value, datetime.date):
                         processed_value = input_value # 已经是正确类型
                     elif isinstance(input_value, datetime.datetime):
                         processed_value = input_value.date() # 提取日期部分
                     else:
                         current_app.logger.error(f"Unsupported input type for Date field '{field_name}': {type(input_value)}")
                         return sql.false()
                elif isinstance(field_type, Time):
                    if isinstance(input_value, str):
                        try:
                            processed_value = datetime.time.fromisoformat(input_value)
                            current_app.logger.debug(f"Parsed Time string '{input_value}' to: {processed_value}")
                        except ValueError:
                            current_app.logger.error(f"Could not parse Time string: '{input_value}' for field '{field_name}'.")
                            return sql.false()
                    elif isinstance(input_value, datetime.time):
                        processed_value = input_value # 已经是正确类型
                    else:
                         current_app.logger.error(f"Unsupported input type for Time field '{field_name}': {type(input_value)}")
                         return sql.false()
                elif isinstance(field_type, (Integer, Float, Double, Numeric)):
                    try:
                        if isinstance(field_type, Integer):
                            # 先尝试转 float 再转 int，以处理可能的 "10.0" 这种输入
                            processed_value = int(float(str(input_value)))
                            current_app.logger.debug(f"Converted '{input_value}' to Integer: {processed_value}")
                        elif isinstance(field_type, (Float, Double)):
                            processed_value = float(str(input_value))
                            current_app.logger.debug(f"Converted '{input_value}' to Float/Double: {processed_value}")
                        elif isinstance(field_type, Numeric):
                            # 使用 decimal 提高精度
                            processed_value = decimal.Decimal(str(input_value))
                            current_app.logger.debug(f"Converted '{input_value}' to Decimal: {processed_value}")
                        else: # Should not happen if type check is exhaustive
                             processed_value = float(str(input_value)) # Fallback to float
                             current_app.logger.warning(f"Unhandled Numeric type {field_type}, converting '{input_value}' to float as fallback.")
                    except (ValueError, TypeError) as conv_err:
                         current_app.logger.error(f"Could not convert '{input_value}' to Number type ({field_type}): {conv_err}")
                         return sql.false() # Fail condition on conversion error
                elif isinstance(field_type, String):
                     processed_value = str(input_value)
                     current_app.logger.debug(f"Using '{input_value}' as String.")
                else:
                     # 未知或不支持的类型，尝试按字符串处理
                     processed_value = str(input_value)
                     current_app.logger.warning(f"Unhandled field type {field_type} for field '{field_name}', treating value as string: {processed_value}")

            except Exception as e:
                current_app.logger.error(f"Error during value processing for field '{field_name}' (value: '{input_value}', type: {field_type}): {e}")
                return sql.false() # Fail condition on unexpected error

            # --- 4. 根据操作符应用条件 (value is guaranteed non-empty here) ---
            current_app.logger.debug(f"Applying operator '{operator}' with processed value: {processed_value} (type: {type(processed_value)}) to field '{field_name}'")
            sql_condition = None
            try:
                if operator == 'eq':
                    sql_condition = (field == processed_value)
                elif operator == 'neq':
                    sql_condition = (field != processed_value)
                elif operator == 'gt':
                    sql_condition = (field > processed_value)
                elif operator == 'gte':
                    sql_condition = (field >= processed_value)
                elif operator == 'lt':
                    sql_condition = (field < processed_value)
                elif operator == 'lte':
                    sql_condition = (field <= processed_value)
                elif operator == 'contains':
                    # Ensure it's a string field for LIKE, value must be string
                    if isinstance(field_type, String):
                        sql_condition = field.ilike(f'%{processed_value}%') # Use ilike for case-insensitive
                    else:
                        current_app.logger.warning(f"'contains' operator used on non-string field '{field_name}'. Condition might fail.")
                        sql_condition = sql.false()
                elif operator == 'not_contains':
                    if isinstance(field_type, String):
                        sql_condition = not_(field.ilike(f'%{processed_value}%'))
                    else:
                        current_app.logger.warning(f"'not_contains' operator used on non-string field '{field_name}'. Condition might fail.")
                        sql_condition = sql.true() # or false()? If it cannot contain, then it doesn't not contain? Let's say true.
                elif operator == 'starts_with':
                    if isinstance(field_type, String):
                        sql_condition = field.ilike(f'{processed_value}%')
                    else:
                        current_app.logger.warning(f"'starts_with' operator used on non-string field '{field_name}'. Condition might fail.")
                        sql_condition = sql.false()
                elif operator == 'ends_with':
                    if isinstance(field_type, String):
                        sql_condition = field.ilike(f'%{processed_value}%')
                    else:
                        current_app.logger.warning(f"'ends_with' operator used on non-string field '{field_name}'. Condition might fail.")
                        sql_condition = sql.false()
                # is_null / is_not_null handled earlier
                # TODO: Add 'in' and 'not_in' operators if needed
                # elif operator == 'in':
                #    # Assuming processed_value is a list or can be split into one
                #    if not isinstance(processed_value, list): processed_value = [processed_value]
                #    sql_condition = field.in_(processed_value)
                # elif operator == 'not_in':
                #    if not isinstance(processed_value, list): processed_value = [processed_value]
                #    sql_condition = field.notin_(processed_value)
                else:
                    current_app.logger.error(f"Unsupported filter operator: {operator}")
                    sql_condition = sql.true() # Or false? Let's be permissive and default to true if operator is unknown

                if sql_condition is not None:
                    current_app.logger.debug(f"[_build_filter_condition] Condition generated: {sql_condition}")
                    return sql_condition
                else:
                    # Should not happen if logic is correct, but as a fallback
                    current_app.logger.error(f"Failed to generate SQL condition for {field_name} {operator} {processed_value}")
                    return sql.false()
            except Exception as e:
                current_app.logger.error(f"Error applying filter operator '{operator}' for field '{field_name}': {e}")
                return sql.false() # Fail condition on error applying operator

        # 处理未知类型
        else:
            current_app.logger.error(f"Unknown filter item type encountered: {filter_type}. Data: {filter_data}")
            return None

    def _apply_advanced_filter(self, query, filter_data):
        """应用高级筛选条件到查询对象"""
        current_app.logger.debug(f"[_apply_advanced_filter] Applying advanced filter: {filter_data}")
        if not filter_data or not isinstance(filter_data, dict):
            return query

        # Get top-level groups and the operators BETWEEN them
        groups_json = filter_data.get('groups', [])
        operators = filter_data.get('operators', []) # List of 'AND' or 'OR' strings
        current_app.logger.debug(f"[_apply_advanced_filter] Received groups: {len(groups_json)}, operators: {operators}")

        if not groups_json:
            return query

        # Build conditions for each top-level group
        group_conditions = []
        current_app.logger.debug(f"[_apply_advanced_filter] Processing {len(groups_json)} top level groups...")
        for i, group_json in enumerate(groups_json):
            current_app.logger.debug(f"[_apply_advanced_filter]  Processing top level group {i}: {group_json}")
            # Ensure each group has a type for _build_filter_condition
            if 'type' not in group_json:
                group_json['type'] = 'group'

            condition = self._build_filter_condition(group_json)
            if condition is not None and condition is not sql.false(): # Exclude always false conditions
                 # Check if the condition is literally `True` which might happen for empty groups
                 # We should probably skip these or handle them based on the operator
                 # For now, let's add them, SQLAlchemy might optimize.
                 # Alternatively, check `str(condition) == str(sql.true())` ?
                 current_app.logger.debug(f"[_apply_advanced_filter]  Condition for group {i}: {condition}")
                 group_conditions.append(condition)
            else:
                current_app.logger.debug(f"[_apply_advanced_filter]  Group {i} resulted in None or False condition.")

        # If no valid conditions generated, return original query
        if not group_conditions:
            current_app.logger.debug(f"[_apply_advanced_filter] No valid group conditions generated.")
            return query

        # Combine the group conditions using the specified operators sequentially, respecting AND precedence
        final_condition = None
        if len(group_conditions) == 1:
            final_condition = group_conditions[0]
            current_app.logger.debug(f"[_apply_advanced_filter] Applying single group condition: {final_condition}")
        elif len(group_conditions) > 1:
            # Check if operator count matches N-1 groups
            if len(operators) == len(group_conditions) - 1:
                # Process with AND having higher precedence than OR
                or_parts = []
                current_and_part = [group_conditions[0]]

                for i in range(len(operators)):
                    op_str = operators[i].upper()
                    next_condition = group_conditions[i+1]

                    if op_str == 'AND':
                        # Add to the current list of conditions to be ANDed together
                        current_and_part.append(next_condition)
                        current_app.logger.debug(f"[_apply_advanced_filter] Adding to AND part: {next_condition}")
                    elif op_str == 'OR':
                        # Finalize the current AND part
                        if len(current_and_part) == 1:
                            anded_condition = current_and_part[0]
                            or_parts.append(anded_condition)
                            current_app.logger.debug(f"[_apply_advanced_filter] Finalized AND part (single): {anded_condition}")
                        elif len(current_and_part) > 1:
                            anded_condition = and_(*current_and_part)
                            or_parts.append(anded_condition)
                            current_app.logger.debug(f"[_apply_advanced_filter] Finalized AND part (multiple): {anded_condition}")
                        # If current_and_part is empty (shouldn't happen with this logic), do nothing

                        # Start a new AND part with the next condition
                        current_and_part = [next_condition]
                        current_app.logger.debug(f"[_apply_advanced_filter] Starting new AND part after OR: {next_condition}")
                    else:
                        current_app.logger.warning(f"Invalid top-level operator '{operators[i]}', treating as AND.")
                        # Treat unknown operator as AND
                        current_and_part.append(next_condition)

                # After the loop, finalize the last AND part
                if len(current_and_part) == 1:
                    last_anded_condition = current_and_part[0]
                    or_parts.append(last_anded_condition)
                    current_app.logger.debug(f"[_apply_advanced_filter] Finalized last AND part (single): {last_anded_condition}")
                elif len(current_and_part) > 1:
                    last_anded_condition = and_(*current_and_part)
                    or_parts.append(last_anded_condition)
                    current_app.logger.debug(f"[_apply_advanced_filter] Finalized last AND part (multiple): {last_anded_condition}")
                # else: Empty last part, do nothing

                # Combine all OR parts
                if not or_parts:
                     # This case should ideally not happen if group_conditions had elements and processing was correct
                     current_app.logger.error("[_apply_advanced_filter] No OR parts generated despite multiple groups? Defaulting to False.")
                     final_condition = sql.false()
                elif len(or_parts) == 1:
                    final_condition = or_parts[0]
                    current_app.logger.debug(f"[_apply_advanced_filter] Final condition is single OR part: {final_condition}")
                else:
                    final_condition = or_(*or_parts)
                    current_app.logger.debug(f"[_apply_advanced_filter] Final condition combines OR parts: {final_condition}")

            else:
                 current_app.logger.error(f"Mismatch between number of groups ({len(group_conditions)}) and operators ({len(operators)}) at top level. Defaulting to AND between all.")
                 # Fallback to AND all groups if operators array is wrong length
                 final_condition = and_(*group_conditions)
                 current_app.logger.debug(f"[_apply_advanced_filter] Final combined condition (fallback AND): {final_condition}")

        # Apply the final combined condition if it exists
        if final_condition is not None:
             current_app.logger.debug(f"[_apply_advanced_filter] Applying final filter: {final_condition}")
             query = query.filter(final_condition)
        else:
             current_app.logger.debug("[_apply_advanced_filter] No final condition to apply.")

        return query