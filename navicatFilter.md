# 类Navicat高级筛选查询功能需求文档

## 1. 产品概述

本文档详细描述Web应用中实现类似Navicat的高级筛选查询功能的产品需求。该功能允许用户通过可视化界面构建复杂的SQL查询条件，支持多层嵌套、多种运算符和灵活的逻辑组合，专为MySQL数据库优化设计。

## 2. 用户界面设计

### 2.1 整体布局结构

高级筛选功能在数据表格右上角通过"筛选"按钮触发，点击后显示类似Navicat的筛选界面：

```
+-----------------------------------------------------------------------+
| 高级筛选                                                      [关闭 X] |
+-----------------------------------------------------------------------+
| [保存] [加载] [清空] [应用] [取消] [生成SQL]                          |
+-----------------------------------------------------------------------+
| 逻辑运算符: [AND ▼]                                                   |
+-----------------------------------------------------------------------+
| [添加条件] [添加条件组]                                               |
+-----------------------------------------------------------------------+
| +------------------------------------------------------------------+  |
| | 条件/条件组 1  [AND ▼]                                       [×] |  |
| |                                                                  |  |
| | [字段▼] [运算符▼] [值输入区        ] [AND▼] [+] [-] [( )] [×]   |  |
| |                                                                  |  |
| | [添加条件] [添加条件组]                                          |  |
| +------------------------------------------------------------------+  |
|                                                                       |
| +------------------------------------------------------------------+  |
| | 条件/条件组 2  [OR ▼]                                        [×] |  |
| |                                                                  |  |
| | [字段▼] [运算符▼] [值输入区        ] [AND▼] [+] [-] [( )] [×]   |  |
| |                                                                  |  |
| | [添加条件] [添加条件组]                                          |  |
| +------------------------------------------------------------------+  |
|                                                                       |
+-----------------------------------------------------------------------+
| [SQL预览区域]                                                         |
| SELECT * FROM table_name WHERE ...                                    |
+-----------------------------------------------------------------------+
```

### 2.2 组件详细说明

#### 2.2.1 顶部工具栏

- **保存按钮**：保存当前筛选条件
- **加载按钮**：加载已保存的筛选条件
- **清空按钮**：清空所有筛选条件
- **应用按钮**：应用当前筛选条件并执行查询
- **取消按钮**：关闭筛选面板不应用条件
- **生成SQL按钮**：生成当前筛选条件的SQL语句

#### 2.2.2 条件项组件

每个条件项包含以下元素：
- **字段选择器**：下拉选择数据表的字段
- **运算符选择器**：根据字段类型显示适用的运算符
- **值输入区**：根据字段类型和运算符显示对应的输入控件
- **逻辑连接器**：选择与下一个条件的连接逻辑（AND/OR）
- **添加按钮**：在当前条件后添加一个新条件
- **删除按钮**：删除当前条件
- **括号控制**：控制条件的括号，选项包括：
  - 不添加括号
  - 在条件前添加开括号
  - 在条件后添加闭括号
  - 在条件前后添加一对括号

#### 2.2.3 条件组组件

条件组是条件项或子条件组的集合，每个条件组包含：
- **条件组标题**：显示"条件组"和编号
- **条件组逻辑选择器**：选择组内条件的连接逻辑（AND/OR）
- **条件项列表**：组内的所有条件和子条件组
- **添加条件按钮**：在组内添加新条件
- **添加条件组按钮**：在组内添加子条件组
- **删除条件组按钮**：删除整个条件组

条件组在UI中通过边框和背景色与普通条件区分，嵌套的条件组通过缩进表示层级关系。

### 2.3 交互行为详细说明

#### 2.3.1 字段选择器行为

1. 初始显示表的所有可筛选字段
2. 选择字段后，根据字段数据类型更新运算符选择器的选项
3. 字段选择变更时，清空值输入区

#### 2.3.2 运算符选择器行为

1. 根据字段类型显示适用的运算符选项
2. 选择运算符后，更新值输入区的类型和格式
3. 特殊运算符（如"为空"、"不为空"）选择后隐藏值输入区

#### 2.3.3 值输入区行为

1. 根据字段类型和运算符显示不同的输入控件：
   - 文本字段：文本输入框或多行文本区
   - 数值字段：数值输入框，支持整数或小数
   - 日期字段：日期选择器
   - 布尔字段：是/否选择器
2. "在列表中"运算符支持多行输入，每行一个值
3. "在范围内"运算符显示最小值和最大值两个输入框

#### 2.3.4 逻辑连接器行为

1. 默认为AND连接
2. 可切换为OR连接
3. 最后一个条件的逻辑连接器禁用

#### 2.3.5 条件组操作行为

1. 条件组内默认包含一个空条件
2. 条件组的逻辑连接器控制组内所有条件之间的关系
3. 嵌套条件组自动使用括号在SQL中表示为一个整体
4. 条件组可以无限嵌套（实际由系统性能限制）

## 3. 筛选条件类型与运算符

### 3.1 文本类型字段 (CHAR, VARCHAR, TEXT)

| 运算符 | SQL实现 | 值输入控件 | 示例SQL |
|--------|--------|------------|---------|
| 等于 | `=` | 文本框 | `name = '测试'` |
| 不等于 | `<>` | 文本框 | `name <> '测试'` |
| 包含 | `LIKE` | 文本框 | `name LIKE '%测试%'` |
| 不包含 | `NOT LIKE` | 文本框 | `name NOT LIKE '%测试%'` |
| 开始于 | `LIKE` | 文本框 | `name LIKE '测试%'` |
| 结束于 | `LIKE` | 文本框 | `name LIKE '%测试'` |
| 为空 | `IS NULL` | 无输入 | `name IS NULL` |
| 不为空 | `IS NOT NULL` | 无输入 | `name IS NOT NULL` |
| 在列表中 | `IN` | 多行文本框 | `name IN ('值1', '值2', '值3')` |
| 不在列表中 | `NOT IN` | 多行文本框 | `name NOT IN ('值1', '值2', '值3')` |
| 正则匹配 | `REGEXP` | 文本框 | `name REGEXP '^[A-Za-z]+'` |

### 3.2 数值类型字段 (INT, FLOAT, DECIMAL)

| 运算符 | SQL实现 | 值输入控件 | 示例SQL |
|--------|--------|------------|---------|
| 等于 | `=` | 数值框 | `price = 100` |
| 不等于 | `<>` | 数值框 | `price <> 100` |
| 大于 | `>` | 数值框 | `price > 100` |
| 大于等于 | `>=` | 数值框 | `price >= 100` |
| 小于 | `<` | 数值框 | `price < 100` |
| 小于等于 | `<=` | 数值框 | `price <= 100` |
| 在范围内 | `BETWEEN` | 两个数值框 | `price BETWEEN 100 AND 200` |
| 不在范围内 | `NOT BETWEEN` | 两个数值框 | `price NOT BETWEEN 100 AND 200` |
| 为空 | `IS NULL` | 无输入 | `price IS NULL` |
| 不为空 | `IS NOT NULL` | 无输入 | `price IS NOT NULL` |
| 在列表中 | `IN` | 多行数值框 | `price IN (100, 200, 300)` |
| 不在列表中 | `NOT IN` | 多行数值框 | `price NOT IN (100, 200, 300)` |

### 3.3 日期时间类型字段 (DATE, DATETIME, TIMESTAMP)

| 运算符 | SQL实现 | 值输入控件 | 示例SQL |
|--------|--------|------------|---------|
| 等于 | `=` | 日期选择器 | `create_date = '2023-01-01'` |
| 不等于 | `<>` | 日期选择器 | `create_date <> '2023-01-01'` |
| 早于 | `<` | 日期选择器 | `create_date < '2023-01-01'` |
| 晚于 | `>` | 日期选择器 | `create_date > '2023-01-01'` |
| 在日期范围内 | `BETWEEN` | 两个日期选择器 | `create_date BETWEEN '2023-01-01' AND '2023-12-31'` |
| 不在日期范围内 | `NOT BETWEEN` | 两个日期选择器 | `create_date NOT BETWEEN '2023-01-01' AND '2023-12-31'` |
| 为空 | `IS NULL` | 无输入 | `create_date IS NULL` |
| 不为空 | `IS NOT NULL` | 无输入 | `create_date IS NOT NULL` |
| 今天 | 函数 | 无输入 | `DATE(create_date) = CURDATE()` |
| 昨天 | 函数 | 无输入 | `DATE(create_date) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)` |
| 本周 | 函数 | 无输入 | `YEARWEEK(create_date, 1) = YEARWEEK(CURDATE(), 1)` |
| 上周 | 函数 | 无输入 | `YEARWEEK(create_date, 1) = YEARWEEK(DATE_SUB(CURDATE(), INTERVAL 1 WEEK), 1)` |
| 本月 | 函数 | 无输入 | `DATE_FORMAT(create_date, '%Y%m') = DATE_FORMAT(CURDATE(), '%Y%m')` |
| 上月 | 函数 | 无输入 | `DATE_FORMAT(create_date, '%Y%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y%m')` |
| 最近N天 | 函数 | 数值框 | `create_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)` |

### 3.4 布尔类型字段 (BOOLEAN, TINYINT(1))

| 运算符 | SQL实现 | 值输入控件 | 示例SQL |
|--------|--------|------------|---------|
| 为真 | `= 1` | 无输入 | `is_active = 1` |
| 为假 | `= 0` | 无输入 | `is_active = 0` |
| 为空 | `IS NULL` | 无输入 | `is_active IS NULL` |
| 不为空 | `IS NOT NULL` | 无输入 | `is_active IS NOT NULL` |

## 4. 详细筛选场景与验收标准

### 4.1 基本条件筛选场景

#### 场景4.1.1：单一条件筛选

**需求描述**：筛选价格大于1000的产品

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件"按钮
3. 从字段下拉列表选择"price"
4. 从运算符下拉列表选择"大于"
5. 在值输入框中输入"1000"
6. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取字段名、运算符和值
2. 根据运算符类型生成相应的SQL条件（数值比较无需引号）
3. 组合最终SQL：`SELECT * FROM products WHERE price > 1000`

**测试验收标准**：
1. 字段下拉列表应显示表的所有可筛选字段
2. 选择数值类型字段后，运算符列表应只显示适用于数值的运算符
3. 值输入区应显示为数值输入框，接受整数或小数
4. 点击"应用"按钮后，SQL查询正确执行
5. 结果集只包含价格大于1000的记录
6. SQL预览区正确显示：`SELECT * FROM products WHERE price > 1000`

#### 场景4.1.2：多条件AND连接

**需求描述**：筛选价格大于1000且库存大于0的产品

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件"按钮
3. 从字段下拉列表选择"price"
4. 从运算符下拉列表选择"大于"
5. 在值输入框中输入"1000"
6. 保持逻辑连接器为"AND"
7. 点击"添加条件"按钮
8. 从字段下拉列表选择"stock"
9. 从运算符下拉列表选择"大于"
10. 在值输入框中输入"0"
11. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取第一个条件：`price > 1000`
2. 获取逻辑连接器：`AND`
3. 获取第二个条件：`stock > 0`
4. 组合最终SQL：`SELECT * FROM products WHERE price > 1000 AND stock > 0`

**测试验收标准**：
1. 两个条件项应正确显示在界面上
2. 第一个条件后的逻辑连接器应设置为"AND"
3. 点击"应用"按钮后，SQL查询正确执行
4. 结果集只包含同时满足两个条件的记录
5. SQL预览区正确显示：`SELECT * FROM products WHERE price > 1000 AND stock > 0`

#### 场景4.1.3：多条件OR连接

**需求描述**：筛选类别为"电子产品"或类别为"家电"的产品

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件"按钮
3. 从字段下拉列表选择"category"
4. 从运算符下拉列表选择"等于"
5. 在值输入框中输入"电子产品"
6. 将逻辑连接器设置为"OR"
7. 点击"添加条件"按钮
8. 从字段下拉列表选择"category"
9. 从运算符下拉列表选择"等于"
10. 在值输入框中输入"家电"
11. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取第一个条件：`category = '电子产品'`
2. 获取逻辑连接器：`OR`
3. 获取第二个条件：`category = '家电'`
4. 组合最终SQL：`SELECT * FROM products WHERE category = '电子产品' OR category = '家电'`

**测试验收标准**：
1. 两个条件项应正确显示在界面上
2. 第一个条件后的逻辑连接器应可以切换并设置为"OR"
3. 点击"应用"按钮后，SQL查询正确执行
4. 结果集包含所有类别为"电子产品"或"家电"的记录
5. SQL预览区正确显示：`SELECT * FROM products WHERE category = '电子产品' OR category = '家电'`

### 4.2 条件组筛选场景

#### 场景4.2.1：简单条件组筛选

**需求描述**：筛选(价格大于1000且库存大于10)或类别为"限量版"的产品

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件组"按钮创建条件组
3. 在条件组内，点击"添加条件"按钮
4. 从字段下拉列表选择"price"
5. 从运算符下拉列表选择"大于"
6. 在值输入框中输入"1000"
7. 保持逻辑连接器为"AND"
8. 点击"添加条件"按钮
9. 从字段下拉列表选择"stock"
10. 从运算符下拉列表选择"大于"
11. 在值输入框中输入"10"
12. 将条件组的逻辑连接器设置为"OR"
13. 点击外层的"添加条件"按钮
14. 从字段下拉列表选择"category"
15. 从运算符下拉列表选择"等于"
16. 在值输入框中输入"限量版"
17. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取条件组内的所有条件：`price > 1000 AND stock > 10`
2. 将条件组用括号包围：`(price > 1000 AND stock > 10)`
3. 获取逻辑连接器：`OR`
4. 获取外层条件：`category = '限量版'`
5. 组合最终SQL：`SELECT * FROM products WHERE (price > 1000 AND stock > 10) OR category = '限量版'`

**测试验收标准**：
1. 条件组和外层条件应有明显的视觉区分
2. 条件组内的条件应正确连接（AND）
3. 条件组和外层条件应正确连接（OR）
4. 生成的SQL应正确添加括号表示条件组
5. SQL预览区正确显示：`SELECT * FROM products WHERE (price > 1000 AND stock > 10) OR category = '限量版'`
6. 执行查询返回的结果集应符合预期

#### 场景4.2.2：条件组嵌套

**需求描述**：筛选((价格<100且评分>4.5)或(价格>1000且评分>4.8))且库存>0的产品

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件组"按钮创建第一个条件组
3. 在第一个条件组内：
   1. 点击"添加条件组"按钮创建第一个子条件组
   2. 在子条件组内：
      1. 点击"添加条件"按钮
      2. 选择字段"price"，运算符"小于"，值"100"
      3. 设置逻辑连接器为"AND"
      4. 点击"添加条件"按钮
      5. 选择字段"rating"，运算符"大于"，值"4.5"
   3. 设置子条件组的逻辑连接器为"OR"
   4. 点击"添加条件组"按钮创建第二个子条件组
   5. 在子条件组内：
      1. 点击"添加条件"按钮
      2. 选择字段"price"，运算符"大于"，值"1000"
      3. 设置逻辑连接器为"AND"
      4. 点击"添加条件"按钮
      5. 选择字段"rating"，运算符"大于"，值"4.8"
4. 设置第一个条件组的逻辑连接器为"AND"
5. 点击外层的"添加条件"按钮
6. 选择字段"stock"，运算符"大于"，值"0"
7. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取第一个子条件组内容：`price < 100 AND rating > 4.5`
2. 用括号包围：`(price < 100 AND rating > 4.5)`
3. 获取第二个子条件组内容：`price > 1000 AND rating > 4.8`
4. 用括号包围：`(price > 1000 AND rating > 4.8)`
5. 使用OR连接两个子条件组：`(price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8)`
6. 将整个第一个条件组用括号包围：`((price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8))`
7. 获取外层条件：`stock > 0`
8. 使用AND连接：`((price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8)) AND stock > 0`
9. 组合最终SQL：`SELECT * FROM products WHERE ((price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8)) AND stock > 0`

**测试验收标准**：
1. 嵌套的条件组应有明显的视觉层次，如缩进或边框颜色深浅变化
2. 各级条件组的逻辑连接器应正确设置和显示
3. 生成的SQL应正确添加多层嵌套的括号
4. SQL预览区正确显示：`SELECT * FROM products WHERE ((price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8)) AND stock > 0`
5. 执行查询返回的结果集应符合预期

### 4.3 特殊运算符筛选场景

#### 场景4.3.1：IN列表匹配

**需求描述**：筛选状态为"已发货"、"已完成"或"已取消"的订单

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件"按钮
3. 从字段下拉列表选择"status"
4. 从运算符下拉列表选择"在列表中"
5. 在多行文本框中输入：
   ```
   已发货
   已完成
   已取消
   ```
6. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取字段名：`status`
2. 获取运算符：`IN`
3. 解析多行文本框的值为列表：`('已发货', '已完成', '已取消')`
4. 组合SQL条件：`status IN ('已发货', '已完成', '已取消')`
5. 生成最终SQL：`SELECT * FROM orders WHERE status IN ('已发货', '已完成', '已取消')`

**测试验收标准**：
1. "在列表中"运算符选择后，值输入区应显示为多行文本框
2. 多行文本框中的每一行应被正确解析为列表中的一项
3. 生成的SQL应正确使用IN运算符和括号
4. SQL预览区正确显示：`SELECT * FROM orders WHERE status IN ('已发货', '已完成', '已取消')`
5. 执行查询返回的结果集应只包含指定状态的订单

#### 场景4.3.2：NULL值处理

**需求描述**：筛选没有填写电话号码的客户（电话为NULL）

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件"按钮
3. 从字段下拉列表选择"phone"
4. 从运算符下拉列表选择"为空"
5. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取字段名：`phone`
2. 获取运算符：`IS NULL`
3. 组合SQL条件：`phone IS NULL`
4. 生成最终SQL：`SELECT * FROM customers WHERE phone IS NULL`

**测试验收标准**：
1. "为空"运算符选择后，值输入区应自动隐藏
2. 生成的SQL应使用IS NULL而非"= NULL"
3. SQL预览区正确显示：`SELECT * FROM customers WHERE phone IS NULL`
4. 执行查询返回的结果集应只包含电话号码为NULL的客户记录

#### 场景4.3.3：日期范围查询

**需求描述**：筛选创建日期在2023年1月1日到2023年12月31日之间的记录

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件"按钮
3. 从字段下拉列表选择"create_date"
4. 从运算符下拉列表选择"在日期范围内"
5. 在第一个日期选择器中选择"2023-01-01"
6. 在第二个日期选择器中选择"2023-12-31"
7. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取字段名：`create_date`
2. 获取运算符：`BETWEEN`
3. 获取开始日期：`'2023-01-01'`
4. 获取结束日期：`'2023-12-31'`
5. 组合SQL条件：`create_date BETWEEN '2023-01-01' AND '2023-12-31'`
6. 生成最终SQL：`SELECT * FROM table_name WHERE create_date BETWEEN '2023-01-01' AND '2023-12-31'`

**测试验收标准**：
1. "在日期范围内"运算符选择后，值输入区应显示两个日期选择器
2. 日期选择器应允许选择有效的日期并以正确格式显示
3. 生成的SQL应正确使用BETWEEN运算符
4. SQL预览区正确显示：`SELECT * FROM table_name WHERE create_date BETWEEN '2023-01-01' AND '2023-12-31'`
5. 执行查询返回的结果集应只包含日期在指定范围内的记录

#### 场景4.3.4：LIKE模糊查询

**需求描述**：筛选名称中包含"智能"的产品

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件"按钮
3. 从字段下拉列表选择"name"
4. 从运算符下拉列表选择"包含"
5. 在值输入框中输入"智能"
6. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取字段名：`name`
2. 获取运算符：`LIKE`
3. 获取值并添加通配符：`'%智能%'`
4. 组合SQL条件：`name LIKE '%智能%'`
5. 生成最终SQL：`SELECT * FROM products WHERE name LIKE '%智能%'`

**测试验收标准**：
1. "包含"运算符选择后，值输入区应显示为文本框
2. 输入的值应自动在生成SQL时添加前后通配符
3. 生成的SQL应正确使用LIKE运算符
4. SQL预览区正确显示：`SELECT * FROM products WHERE name LIKE '%智能%'`
5. 执行查询返回的结果集应只包含名称中包含"智能"的产品

### 4.4 复杂多层嵌套场景

#### 场景4.4.1：三层嵌套条件

**需求描述**：筛选(((价格<100且评分>4.5)或(价格>1000且评分>4.8))且(类别="电子产品"或类别="家电"))或((品牌="A"或品牌="B")且库存>100)的产品

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 点击"添加条件组"按钮创建第一个顶级条件组A
3. 在条件组A内：
   1. 点击"添加条件组"按钮创建子条件组A1
   2. 在子条件组A1内：
      1. 点击"添加条件组"按钮创建子条件组A1-1
      2. 在子条件组A1-1内：
         1. 添加条件：price < 100 AND rating > 4.5
      3. 设置子条件组A1-1的逻辑连接器为"OR"
      4. 点击"添加条件组"按钮创建子条件组A1-2
      5. 在子条件组A1-2内：
         1. 添加条件：price > 1000 AND rating > 4.8
   3. 设置子条件组A1的逻辑连接器为"AND"
   4. 点击"添加条件组"按钮创建子条件组A2
   5. 在子条件组A2内：
      1. 添加条件：category = "电子产品" OR category = "家电"
4. 设置顶级条件组A的逻辑连接器为"OR"
5. 点击"添加条件组"按钮创建第二个顶级条件组B
6. 在条件组B内：
   1. 点击"添加条件组"按钮创建子条件组B1
   2. 在子条件组B1内：
      1. 添加条件：brand = "A" OR brand = "B"
   3. 设置子条件组B1的逻辑连接器为"AND"
   4. 添加条件：stock > 100
7. 点击"应用"按钮

**SQL构建逻辑**：
1. 构建子条件组A1-1: `(price < 100 AND rating > 4.5)`
2. 构建子条件组A1-2: `(price > 1000 AND rating > 4.8)`
3. 合并A1-1和A1-2: `((price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8))`
4. 构建子条件组A2: `(category = '电子产品' OR category = '家电')`
5. 合并A1和A2: `(((price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8)) AND (category = '电子产品' OR category = '家电'))`
6. 构建子条件组B1: `(brand = 'A' OR brand = 'B')`
7. 合并B1和条件: `((brand = 'A' OR brand = 'B') AND stock > 100)`
8. 合并顶级条件组A和B: `(((price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8)) AND (category = '电子产品' OR category = '家电')) OR ((brand = 'A' OR brand = 'B') AND stock > 100)`
9. 生成最终SQL: `SELECT * FROM products WHERE (((price < 100 AND rating > 4.5) OR (price > 1000 AND rating > 4.8)) AND (category = '电子产品' OR category = '家电')) OR ((brand = 'A' OR brand = 'B') AND stock > 100)`

**测试验收标准**：
1. 界面应能清晰显示三层嵌套的条件组结构
2. 各级条件组之间的层级关系应有明显的视觉区分
3. 各级条件组内部和之间的逻辑连接器应正确设置和显示
4. 生成的SQL应正确添加多层嵌套的括号，保证运算符优先级正确
5. SQL预览区正确显示完整SQL，与上述生成逻辑一致
6. 执行查询返回的结果集应符合这个复杂条件

#### 场景4.4.2：混合各种运算符的复杂条件

**需求描述**：筛选((价格在100-500之间且品牌不为空)或(价格>1000且销量>100))且((创建日期在最近30天内且状态IN("已上架","促销中"))或评分>4.8)的产品

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 创建嵌套条件组结构，添加各类条件
   [详细步骤略，因操作过于复杂]

**SQL构建逻辑**：
1. 构建价格范围条件: `price BETWEEN 100 AND 500`
2. 构建品牌不为空条件: `brand IS NOT NULL`
3. 合并条件1: `(price BETWEEN 100 AND 500 AND brand IS NOT NULL)`
4. 构建价格大于条件: `price > 1000`
5. 构建销量条件: `sales > 100`
6. 合并条件2: `(price > 1000 AND sales > 100)`
7. 合并条件组1: `((price BETWEEN 100 AND 500 AND brand IS NOT NULL) OR (price > 1000 AND sales > 100))`
8. 构建日期条件: `create_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)`
9. 构建状态IN条件: `status IN ('已上架', '促销中')`
10. 合并条件3: `(create_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND status IN ('已上架', '促销中'))`
11. 构建评分条件: `rating > 4.8`
12. 合并条件组2: `((create_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND status IN ('已上架', '促销中')) OR rating > 4.8)`
13. 合并最终条件: `(((price BETWEEN 100 AND 500 AND brand IS NOT NULL) OR (price > 1000 AND sales > 100)) AND ((create_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND status IN ('已上架', '促销中')) OR rating > 4.8))`
14. 生成最终SQL: `SELECT * FROM products WHERE (((price BETWEEN 100 AND 500 AND brand IS NOT NULL) OR (price > 1000 AND sales > 100)) AND ((create_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND status IN ('已上架', '促销中')) OR rating > 4.8))`

**测试验收标准**：
1. 界面应能支持创建和显示如此复杂的条件组合
2. 所有不同类型的运算符（范围、IN、IS NOT NULL、日期函数等）应正确生成SQL
3. 复杂嵌套条件的括号应正确添加，确保SQL语法和逻辑正确
4. SQL预览区应正确显示完整的SQL，各部分的运算符优先级正确
5. 执行查询返回的结果集应符合这个复杂条件

#### 场景4.4.3：具有并列关系的多组嵌套条件

**需求描述**：实现四组并列的条件组，每组都有自己的嵌套结构，通过OR连接

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 创建四个顶级条件组，每组内创建子条件组
   [详细步骤略，因操作过于复杂]

**SQL构建逻辑**：
构建四个独立的条件组SQL，然后通过OR连接它们：
```sql
SELECT * FROM products 
WHERE (条件组1的条件) 
   OR (条件组2的条件) 
   OR (条件组3的条件) 
   OR (条件组4的条件)
```

**测试验收标准**：
1. 界面应能支持创建和显示多个并列的条件组
2. 每个条件组内的嵌套结构应正确显示
3. 条件组之间的逻辑连接器应正确设置为OR
4. 生成的SQL应正确添加括号，确保每个条件组作为一个独立单元
5. SQL预览区应正确显示完整的SQL，各条件组之间的关系清晰
6. 执行查询返回的结果集应符合预期

### 4.5 括号控制场景

#### 场景4.5.1：手动添加单条件括号

**需求描述**：手动为单个条件添加括号，影响条件的优先级

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 添加三个条件：
   1. price > 100
   2. category = '电子产品'
   3. stock > 0
3. 为第二个条件选择括号控制"前后添加括号"
4. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取条件1: `price > 100`
2. 获取逻辑连接器1: `AND`
3. 获取条件2并添加括号: `(category = '电子产品')`
4. 获取逻辑连接器2: `AND`
5. 获取条件3: `stock > 0`
6. 组合最终SQL: `SELECT * FROM products WHERE price > 100 AND (category = '电子产品') AND stock > 0`

**测试验收标准**：
1. 界面应提供括号控制选项
2. 选择"前后添加括号"后，条件在UI中应有明显的括号标识
3. 生成的SQL应在指定条件前后添加括号
4. SQL预览区正确显示：`SELECT * FROM products WHERE price > 100 AND (category = '电子产品') AND stock > 0`
5. 执行查询返回的结果集应符合预期

#### 场景4.5.2：手动添加多条件括号

**需求描述**：手动为连续的多个条件添加括号

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 添加三个条件：
   1. price > 100
   2. category = '电子产品'
   3. stock > 0
3. 为第一个条件选择括号控制"前添加开括号"
4. 为第二个条件选择括号控制"后添加闭括号"
5. 点击"应用"按钮

**SQL构建逻辑**：
1. 获取条件1并添加开括号: `(price > 100`
2. 获取逻辑连接器1: `AND`
3. 获取条件2并添加闭括号: `category = '电子产品')`
4. 获取逻辑连接器2: `AND`
5. 获取条件3: `stock > 0`
6. 组合最终SQL: `SELECT * FROM products WHERE (price > 100 AND category = '电子产品') AND stock > 0`

**测试验收标准**：
1. 界面应提供"前添加开括号"和"后添加闭括号"选项
2. 选择这些选项后，条件在UI中应有明显的括号标识
3. 生成的SQL应在指定位置正确添加开闭括号
4. SQL预览区正确显示：`SELECT * FROM products WHERE (price > 100 AND category = '电子产品') AND stock > 0`
5. 执行查询返回的结果集应符合预期

#### 场景4.5.3：条件组与手动括号混合

**需求描述**：条件组自动括号和手动添加括号混合使用

**UI操作流程**：
1. 点击"筛选"按钮打开筛选面板
2. 添加条件组并在内部添加条件
3. 在外部添加条件并使用括号控制
4. 点击"应用"按钮

**SQL构建逻辑**：
同时处理条件组的自动括号和手动添加的括号，确保逻辑正确：
```sql
SELECT * FROM products WHERE (条件组内容) AND (手动括号内容)
```

**测试验收标准**：
1. 界面应能同时支持条件组和手动括号控制
2. 条件组应自动添加括号
3. 手动括号控制应正确应用
4. 生成的SQL应包含所有必要的括号，确保逻辑正确
5. SQL预览区应正确显示完整SQL
6. 执行查询返回的结果集应符合预期

## 5. SQL生成规则

### 5.1 基本SQL生成原则

1. **字段名处理**：
   - 所有字段名用反引号（\`）包围，避免与MySQL关键字冲突
   - 例：\`name\`、\`order\`、\`group\`等

2. **字符串值处理**：
   - 所有字符串值用单引号（'）包围
   - 自动转义字符串中的单引号字符，防止SQL注入
   - 例：'O\'Reilly'

3. **数值处理**：
   - 整数和浮点数不加引号
   - 科学计数法正确处理

4. **NULL值处理**：
   - 使用IS NULL和IS NOT NULL而非"= NULL"或"<> NULL"

5. **日期时间处理**：
   - 日期格式统一为'YYYY-MM-DD'
   - 日期时间格式统一为'YYYY-MM-DD HH:MM:SS'
   - 使用MySQL日期函数进行日期计算和比较

6. **逻辑运算符优先级**：
   - 使用括号明确表示运算优先级
   - AND优先级高于OR，在混合使用时总是添加括号明确分组

7. **括号匹配**：
   - 条件组自动添加外层括号
   - 手动添加的括号与自动括号协调处理
   - 确保所有开括号和闭括号正确匹配

### 5.2 SQL生成算法

1. **条件项SQL生成**：
   - 根据字段类型、运算符和值生成单个条件SQL
   - 特殊运算符使用对应的MySQL语法
   - 处理字段名和值的引号、转义等

2. **条件组SQL生成**：
   - 递归处理嵌套条件组
   - 用组内设置的逻辑运算符连接所有条件
   - 为每个条件组添加括号

3. **括号处理**：
   - 条件组自动添加括号
   - 根据用户设置添加手动括号
   - 合并连续的括号，避免冗余

4. **SQL优化**：
   - 移除不必要的括号
   - 格式化SQL提高可读性
   - 处理特殊情况（如单条件不需要括号）

## 6. 数据结构

### 6.1 前端筛选条件数据结构

```javascript
// 条件项
{
  type: "condition",
  id: "unique_id",
  field: "field_name",
  operator: "operator_name",
  value: "value",
  logic: "AND" // 与下一个条件的连接逻辑
}

// 条件组
{
  type: "group",
  id: "unique_id",
  logic: "AND", // 组内条件的连接逻辑
  items: [ // 可以包含条件项或子条件组
    {
      type: "condition",
      ...
    },
    {
      type: "group",
      ...
    }
  ],
  nextLogic: "AND" // 与下一个元素的连接逻辑
}

// 整体筛选条件
{
  table: "table_name",
  conditions: [ // 顶级条件项或条件组列表
    {
      type: "condition",
      ...
    },
    {
      type: "group",
      ...
    }
  ]
}
```

### 6.2 保存的筛选条件结构

```javascript
{
  id: "unique_id",
  name: "筛选名称",
  description: "筛选描述",
  table: "table_name",
  created_at: "2023-01-01 12:00:00",
  updated_at: "2023-01-02 12:00:00",
  conditions: { /* 筛选条件结构 */ }
}
```

## 7. 技术实现要点

### 7.1 前端技术要点

1. **组件化设计**：
   - 使用组件化设计条件项和条件组
   - 支持组件的递归渲染（条件组内嵌套条件组）

2. **状态管理**：
   - 管理复杂的条件结构状态
   - 处理条件变更、添加、删除等操作

3. **界面优化**：
   - 清晰显示嵌套层级
   - 适当使用缩进、边框、背景色区分层级
   - 确保复杂结构下的良好可读性

4. **表单控件**：
   - 根据字段类型和运算符动态显示不同控件
   - 支持多行输入、日期选择器等特殊控件

5. **SQL预览**：
   - 实时生成并显示SQL预览
   - 支持语法高亮

### 7.2 后端技术要点

1. **SQL生成**：
   - 根据前端传递的条件结构生成MySQL查询
   - 处理特殊字符、转义和防注入
   - 优化生成的SQL

2. **条件存储**：
   - 存储和检索用户保存的筛选条件
   - 版本控制和更新机制

3. **安全处理**：
   - 参数化查询防注入
   - 字段名和表名验证
   - 复杂查询的性能保护

## 8. API接口

### 8.1 获取表字段信息

**请求**：
```
GET /api/tables/{tableName}/fields
```

**响应**：
```json
{
  "fields": [
    {"name": "id", "type": "int", "label": "ID"},
    {"name": "name", "type": "varchar", "label": "名称"},
    {"name": "price", "type": "decimal", "label": "价格"},
    {"name": "create_date", "type": "datetime", "label": "创建日期"}
  ]
}
```

### 8.2 执行筛选查询

**请求**：
```
POST /api/tables/{tableName}/filter
```

**请求体**：
```json
{
  "conditions": { /* 筛选条件结构 */ },
  "pagination": { "page": 1, "pageSize": 20 },
  "sort": { "field": "id", "order": "desc" }
}
```

**响应**：
```json
{
  "total": 100,
  "page": 1,
  "pageSize": 20,
  "data": [ /* 数据记录 */ ],
  "sql": "SELECT * FROM ... WHERE ..."
}
```

### 8.3 保存筛选条件

**请求**：
```
POST /api/filters
```

**请求体**：
```json
{
  "name": "筛选名称",
  "description": "筛选描述",
  "table": "table_name",
  "conditions": { /* 筛选条件结构 */ }
}
```

**响应**：
```json
{
  "id": "filter_id",
  "message": "筛选条件保存成功"
}
```

### 8.4 获取保存的筛选条件列表

**请求**：
```
GET /api/filters?table={tableName}
```

**响应**：
```json
{
  "filters": [
    {
      "id": "filter_id_1",
      "name": "筛选1",
      "description": "描述1",
      "created_at": "2023-01-01 12:00:00"
    },
    {
      "id": "filter_id_2",
      "name": "筛选2",
      "description": "描述2",
      "created_at": "2023-01-02 12:00:00"
    }
  ]
}
```

### 8.5 加载筛选条件

**请求**：
```
GET /api/filters/{filterId}
```

**响应**：
```json
{
  "id": "filter_id",
  "name": "筛选名称",
  "description": "筛选描述",
  "table": "table_name",
  "conditions": { /* 筛选条件结构 */ }
}
```

## 9. 完整验收标准

### 9.1 功能验收

1. 支持所有规定的字段类型和运算符
2. 支持条件组嵌套（至少5层）
3. 支持手动和自动括号控制
4. 支持复杂多条件组合查询
5. 支持保存、加载和管理查询条件
6. 在复杂查询下正确生成SQL语句
7. 实时显示SQL预览
8. 支持全部测试场景

### 9.2 界面验收

1. 布局与Navicat筛选功能类似
2. 嵌套条件组有明显的层级视觉区分
3. 条件项和条件组的操作按钮位置合理
4. 不同类型字段显示适当的输入控件
5. 复杂条件结构下保持良好可读性
6. 响应式设计，适应不同屏幕大小

### 9.3 性能验收

1. 复杂条件（多层嵌套、多条件）下界面响应流畅
2. SQL生成速度快，实时预览无明显延迟
3. 支持至少50个条件项的复杂查询
4. 支持至少5层嵌套的条件组

### 9.4 安全验收

1. 防SQL注入
2. 特殊字符正确处理
3. 复杂查询的性能保护措施
4. 字段和表名验证