import datetime
import decimal
from sqlalchemy.inspection import inspect

def serialize_model(model):
    """
    将 SQLAlchemy 模型实例转换为可序列化为 JSON 的字典。
    处理常见的特殊类型如 datetime, date, Decimal。
    """
    if model is None:
        return None

    try:
        # 使用 inspect 获取模型的持久化属性（通常对应列）
        mapper = inspect(model.__class__)
        columns = [c.key for c in mapper.attrs] # 获取所有映射属性的名称
    except Exception as e:
         # Fallback or error if inspect fails (should not happen for mapped classes)
         print(f"Warning: Could not inspect model columns for {model.__class__.__name__}: {e}. Falling back to __dict__ (may include unwanted state).")
         columns = [key for key in model.__dict__.keys() if not key.startswith('_')]


    result = {}
    for key in columns:
        value = getattr(model, key, None)

        # 类型转换
        if isinstance(value, (datetime.datetime, datetime.date)):
            result[key] = value.isoformat()
        elif isinstance(value, decimal.Decimal):
            result[key] = str(value) # 或者 float(value)，取决于前端需求
        # elif isinstance(value, bytes): # Example for handling bytes if needed
        #     result[key] = value.decode('utf-8', errors='replace')
        else:
            # 假设其他基本类型 (int, float, bool, str, None) 是 JSON 可序列化的
            result[key] = value

    return result

# Example Usage (for testing):
# class MockModel:
#     def __init__(self):
#         self.id = 1
#         self.name = "Test"
#         self.created_at = datetime.datetime(2023, 1, 1, 12, 0, 0)
#         self.value = decimal.Decimal("123.45")
#         self.is_active = True
#         self.description = None
#         self._sa_instance_state = "some_internal_state" # To test filtering if fallback used

# # Mock inspect functionality for testing standalone
# class MockMapper:
#     class MockAttr:
#         def __init__(self, key):
#             self.key = key
#     attrs = [MockAttr('id'), MockAttr('name'), MockAttr('created_at'), MockAttr('value'), MockAttr('is_active'), MockAttr('description')]

# original_inspect = inspect
# inspect = lambda x: MockMapper() # Mock inspect

# mock = MockModel()
# serialized = serialize_model(mock)
# print(serialized)
# # Expected: {'id': 1, 'name': 'Test', 'created_at': '2023-01-01T12:00:00', 'value': '123.45', 'is_active': True, 'description': None}

# inspect = original_inspect # Restore inspect 