#!/bin/bash

echo "=== OpenAI API密钥验证测试 ==="
echo "开始时间: $(date)"
echo ""

# 设置工作目录
cd "$(dirname "$0")"

echo "当前目录: $(pwd)"
echo "检查文件存在性:"
echo "- check.sh: $([ -f check.sh ] && echo "存在" || echo "不存在")"
echo "- test_keys.txt: $([ -f test_keys.txt ] && echo "存在" || echo "不存在")"
echo ""

echo "测试密钥文件内容:"
echo "----------------------------------------"
head -10 test_keys.txt
echo "----------------------------------------"
echo ""

echo "开始执行API验证测试..."
echo "========================================"

# 执行主脚本
timeout 120 bash check.sh test_keys.txt 2>&1

echo ""
echo "========================================"
echo "测试完成时间: $(date)"

# 检查生成的文件
echo ""
echo "生成的文件:"
ls -la *.log *.txt 2>/dev/null | grep -E "(api_test_|valid_keys_)" || echo "没有找到生成的文件"
