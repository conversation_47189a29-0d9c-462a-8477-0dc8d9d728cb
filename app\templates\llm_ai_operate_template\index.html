{% extends "base.html" %}
{% block title %}数据操作表管理{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .sidebar {
        background-color: #f8f9fa;
        height: 100vh;
        border-right: 1px solid #dee2e6;
    }
    .nav-link {
        color: #495057;
    }
    .nav-link.active {
        color: #007bff;
        font-weight: bold;
    }
    .main-content {
        padding: 20px;
    }
    .operation-bar {
        margin-bottom: 20px;
    }
    .api-section {
        display: none;
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
    .json-editor {
        width: 100%;
        height: 300px;
        font-family: monospace;
    }
    .column-toggle-dropdown .dropdown-menu {
        padding: 10px;
        min-width: 200px;
    }
    .dataTables_wrapper .dt-buttons {
        margin-bottom: 10px;
    }
    .custom-control {
        margin-bottom: 5px;
    }
    .action-buttons {
        white-space: nowrap;
    }
    
    /* 高级筛选样式 */
    .filter-group {
        border: 1px dashed #ccc;
        padding: 10px;
        border-radius: 5px;
        background-color: #f8f9fa;
        margin-bottom: 15px;
    }
    
    .filter-group .filter-group {
        margin-left: 0px;
        background-color: #fff;
    }
    
    .filter-group-header {
        background-color: #f0f0f0;
        padding: 8px 12px;
        border-radius: 4px;
        margin-bottom: 12px;
        font-weight: 500;
    }
    
    .filter-conditions {
        padding-left: 15px;
    }
    
    .filter-condition {
        margin-bottom: 8px;
        padding: 8px;
        border-radius: 4px;
        background-color: #fff;
        transition: background-color 0.2s;
    }
    
    .filter-condition:hover {
        background-color: #f0f0f0;
    }
    
    .filter-field {
        min-width: 120px;
    }
    
    .filter-operator {
        min-width: 100px;
    }
    
    .filter-value {
        min-width: 150px;
    }
    
    .filter-actions {
        white-space: nowrap;
    }
    
    /* 让搜索框在DataTables工具栏的左侧 */
    .dataTables_length {
        padding: 5px 0;
        display: flex;
        align-items: center;
    }
    
    /* 调整DataTables的DOM布局以适应新的搜索框位置 */
    .dt-buttons { float: right; margin-left: 10px; } /* 按钮放右边 */
    .dataTables_filter { float: left; text-align: left; } /* 搜索框放左边 */
    .dataTables_length { float: right; text-align: right; } /* 长度选择放右边 */
    .dataTables_info { clear: both; padding-top: 8px !important; } /* 信息换行显示 */
    .dataTables_paginate { float: right; text-align: right; padding-top: 5px !important; } /* 分页放右边 */

    .dataTables_info {
        padding-top: 8px !important;
        color: #495057;
    }
</style>
<!-- 添加Font Awesome图标 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 左侧边栏 -->
        <div class="col-md-2 sidebar py-3">
            <h5 class="mb-3">功能目录</h5>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#">数据操作表管理</a>
                </li>
            </ul>
        </div>
        
        <!-- 右侧主内容区 -->
        <div class="col-md-10 main-content">
            <h2>数据操作表管理</h2>
            
            <!-- 操作菜单 -->
            <div class="operation-bar">
                <button id="btnDataOp" class="btn btn-success">批量录入</button>
                <button id="btnAdd" class="btn btn-primary">添加</button>
                <button id="btnEdit" class="btn btn-secondary" disabled>编辑</button>
                <button id="btnDelete" class="btn btn-danger" disabled>删除</button>
                <button id="btnTop" class="btn btn-info" disabled>置顶</button>
                <button id="btnBottom" class="btn btn-info" disabled>置底</button>
                <button id="btnSync" class="btn btn-warning" disabled>标记同步</button>
                
                <!-- 列显示控制下拉框 -->
                <div class="btn-group column-toggle-dropdown float-right">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        显示/隐藏列
                    </button>
                    <div class="dropdown-menu dropdown-menu-right">
                        <!-- 这里的复选框会通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
            
            <!-- API调用区域 -->
            <div id="apiSection" class="api-section">
                <div class="form-group row">
                    <label class="col-sm-2 col-form-label">API Base</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="apiBase" placeholder="https://api.test.com/v1/models">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 col-form-label">API Key</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="apiKey" placeholder="sk-...">
                    </div>
                </div>
                <div class="form-group">
                    <button id="btnFetchData" class="btn btn-primary">拉取模型数据</button>
                </div>
                <div class="form-group">
                    <label>模型数据 JSON</label>
                    <textarea id="jsonEditor" class="json-editor"></textarea>
                </div>
                <div class="form-group row">
                    <div class="col-sm-2">
                        <button id="btnLevelFilter" class="btn btn-success">筛选 - 层级</button>
                    </div>
                    <div class="col-sm-8">
                        <input type="number" class="form-control" id="levelFilter" value="1" placeholder="层级数值-从1开始，如：1">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-2">
                        <button id="btnFieldFilter" class="btn btn-success">筛选 - 字段</button>
                    </div>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" id="fieldFilter" value="id" placeholder="字段名称，如：model_id">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-2">
                        <button id="btnConfirmFilter" class="btn btn-primary">筛选数据入库</button>
                    </div>
                    <div class="col-sm-8">
                        <span >！注意： 不会导入重复的数据</span>
                    </div>
                </div>
            </div>
            
            <!-- 高级筛选区域 -->
            <div id="advancedFilterSection" class="mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">高级筛选</h5>
                        <div>
                            <button id="btnApplyAdvancedFilter" class="btn btn-sm btn-success">应用筛选</button>
                            <button id="btnResetAdvancedFilter" class="btn btn-sm btn-danger">重置</button>
                            <button id="btnTestFilter" class="btn btn-sm btn-info mr-2">测试</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="filterContainer" class="filter-container">                           
                            <!-- 顶层条件组容器 -->
                            <div id="topLevelGroups">
                                <!-- 条件组将会动态添加到这里 -->
                            </div>
                            
                            <!-- 添加条件组按钮 -->
                            <div class="text-center mt-3">
                                <button id="btnAddTopLevelGroup" class="btn btn-outline-primary">
                                    <i class="fas fa-plus-circle"></i> 添加顶层条件组
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 响应式数据表格 -->
            <div class="table-responsive">
                <table id="dataTable" class="table table-striped table-bordered" width="100%">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>ID</th>
                            <th>模型名称</th>
                            <th>排序顺序</th>
                            <th>同步状态</th>
                            <th>创建时间</th>
                            <th>创建人</th>
                            <th>更新时间</th>
                            <th>更新人</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过DataTables动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑模态框 -->
<div class="modal fade" id="modelModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">添加模型</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="modelForm">
                    <input type="hidden" id="modelId">
                    <div class="form-group">
                        <label>模型名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="modelName" required>
                    </div>
                    <div class="form-group">
                        <label>排序顺序</label>
                        <input type="number" class="form-control" id="modelSortOrder">
                    </div>
                    <div class="form-group">
                        <label>同步状态</label>
                        <select class="form-control" id="modelNeedSync">
                            <option value="0">无需同步</option>
                            <option value="1">需要同步</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="modelIsDeleted">
                            <label class="custom-control-label" for="modelIsDeleted">标记为删除</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btnSaveModel">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 确认操作模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmTitle">确认操作</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btnConfirm">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
$(document).ready(function() {
    // 初始化DataTables
    var table = $('#dataTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        dom: '<"row mb-3"<"col-sm-12 col-md-6"f><"col-sm-12 col-md-6"lB>><"row"<"col-sm-12"tr>><"row mt-3"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        ajax: {
            url: '/module_operate/data',
            type: 'GET',
            data: function(d) {
                // 构建高级筛选条件
                const filterJson = getFilterJson();
                // 只有当有有效条件时才应用筛选
                let hasValidConditions = false;
                
                if (filterJson.groups && filterJson.groups.length > 0) {
                    // 检查是否有有效条件
                    for (const group of filterJson.groups) {
                        if (group.conditions && group.conditions.length > 0) {
                            hasValidConditions = true;
                            break;
                        }
                    }
                }
                
                if (hasValidConditions) {
                    // 将最新的筛选条件存储到localStorage
                    const filterString = JSON.stringify(filterJson);
                    localStorage.setItem('advanced_filter', filterString);
                    console.log('Applying filter:', filterString);
                    d.advanced_filter = filterString;
                } //else {// 如果没有有效条件，尝试从localStorage加载
                    
                //    const savedFilter = localStorage.getItem('advanced_filter');
                //    if (savedFilter) {
                //        console.log('Using saved filter:', savedFilter);
                //        d.advanced_filter = savedFilter;
                //    }
                //}
                
                return d;
            }
        },
        columns: [
            { 
                data: null, 
                orderable: false,
                render: function(data) {
                    return '<input type="checkbox" class="select-item" data-id="' + data.id + '">';
                }
            },
            { data: 'id' },
            { data: 'model_name' },
            { 
                data: 'sort_order',
                render: function(data) {
                    return data === null ? '-' : data;
                }
            },
            { 
                data: 'need_sync',
                render: function(data) {
                    if (data === 1) {
                        return '<span class="badge badge-warning">需同步</span>';
                    } else {
                        return '<span class="badge badge-secondary">无需同步</span>';
                    }
                }
            },
            { data: 'create_time' },
            { 
                data: 'create_by',
                render: function(data) {
                    return data || '-';
                }
            },
            { data: 'update_time' },
            { 
                data: 'update_by',
                render: function(data) {
                    return data || '-';
                }
            },
            { 
                data: 'is_deleted',
                render: function(data) {
                    if (data === 1) {
                        return '<span class="badge badge-danger">已删除</span>';
                    } else {
                        return '<span class="badge badge-success">正常</span>';
                    }
                }
            },
            { 
                data: null,
                orderable: false,
                render: function(data) {
                    return '<div class="action-buttons">' +
                           '<button class="btn btn-sm btn-primary btn-edit" data-id="' + data.id + '">编辑</button> ' +
                           '<button class="btn btn-sm btn-danger btn-delete" data-id="' + data.id + '">删除</button> ' +
                           '<button class="btn btn-sm btn-info btn-sync" data-id="' + data.id + '" data-sync="' + (data.need_sync === 1 ? '0' : '1') + '">' + 
                           (data.need_sync === 1 ? '取消同步' : '标记同步') + '</button>' +
                           '</div>';
                }
            }
        ],
        order: [[3, 'asc']],
        language: {
            url: '{{ url_for("static", filename="other/zh.json") }}',
            info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            infoEmpty: "没有数据",
            infoFiltered: "(从 _MAX_ 条数据中筛选)",
            processing: "处理中...",
            search: "搜索:",
            lengthMenu: "显示 _MENU_ 项",
            paginate: {
                first: "首页",
                previous: "上页",
                next: "下页",
                last: "末页"
            }
        },
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        lengthMenu: [[10, 25, 50, 100, 200, 500, 1000, 3000, -1], 
                    [10, 25, 50, 100, 200, 500, 1000, 3000, "全部"]],
        pageLength: 25,
        stateSave: true,
        stateDuration: 60 * 60 * 24, // 1天
        initComplete: function() {
            // 初始化列显示控制
            this.api().columns().every(function(index) {
                if (index === 0 || index === 10) return; // 跳过复选框列和操作列
                
                var column = this;
                var title = $(column.header()).text();
                
                var checkbox = $('<div class="custom-control custom-checkbox">' +
                                '<input type="checkbox" class="custom-control-input" id="toggle-col-' + index + '" checked>' +
                                '<label class="custom-control-label" for="toggle-col-' + index + '">' + title + '</label>' +
                                '</div>');
                
                $('.column-toggle-dropdown .dropdown-menu').append(checkbox);
                
                checkbox.find('input').on('change', function() {
                    var isVisible = $(this).prop('checked');
                    column.visible(isVisible);
                    // 保存状态到本地存储
                    localStorage.setItem('dt_col_' + index, isVisible ? '1' : '0');
                });
                
                // 从本地存储加载列可见性状态
                var savedState = localStorage.getItem('dt_col_' + index);
                if (savedState === '0') {
                    column.visible(false);
                    checkbox.find('input').prop('checked', false);
                }
            });
        },
        onDraw: function() {
            if ($('.custom-page-length').length === 0) {
                $('.dataTables_length').append(
                    '<div class="custom-page-length d-inline-flex align-items-center ml-2">' +
                    '<input type="number" min="1" max="10000" class="form-control form-control-sm custom-length-input" placeholder="自定义">' +
                    '<button class="btn btn-sm btn-outline-secondary ml-1 apply-custom-length">确定</button>' +
                    '</div>'
                );
                
                // 应用到所有DataTables长度选择框
                $('.dataTables_length').css({
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'flex-end'
                });
                
                // 为底部分页信息也添加自定义页长
                if ($('.dataTables_wrapper .row:last-child .custom-page-length').length === 0) {
                    $('.dataTables_wrapper .row:last-child .dataTables_length').append(
                        $('.custom-page-length').first().clone(true)
                    );
                }
            }
        }
    });
    
    // 选择项变化时更新按钮状态
    function updateButtonState() {
        var selectedCount = $('.select-item:checked').length;
        $('#btnEdit, #btnDelete, #btnTop, #btnBottom, #btnSync').prop('disabled', selectedCount === 0);
        
        // 如果只选中一项，启用编辑按钮
        $('#btnEdit').prop('disabled', selectedCount !== 1);
    }
    
    // 监听复选框变化
    $(document).on('change', '.select-item, #selectAll', updateButtonState);
    
    // 全选/取消全选
    $('#selectAll').change(function() {
        $('.select-item').prop('checked', $(this).is(':checked'));
        updateButtonState();
    });
    
    // 数据操作按钮点击
    $('#btnDataOp').click(function() {
        $('#apiSection').toggle();
    });
    
    // 拉取数据按钮点击
    $('#btnFetchData').click(function() {
        const apiBase = $('#apiBase').val();
        const apiKey = $('#apiKey').val();
        
        if (!apiBase || !apiKey) {
            showToast('提示', '请输入API Base和API Key', 'warning');
            return;
        }
        
        $.ajax({
            url: '/module_operate/fetch_models',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                api_base: apiBase,
                api_key: apiKey
            }),
            success: function(res) {
                if (res.success) {
                    $('#jsonEditor').val(JSON.stringify(res.models, null, 2));
                    showToast('成功', '模型数据获取成功', 'success');
                } else {
                    showToast('错误', '获取失败: ' + res.message, 'danger');
                }
            },
            error: function(err) {
                showToast('错误', '请求失败: ' + (err.responseJSON?.message || err.statusText), 'danger');
            }
        });
    });
    
    // 筛选确认按钮点击
    $('#btnConfirmFilter').click(function() {
        const levelValue = $('#levelFilter').val();
        const fieldName = $('#fieldFilter').val();
        const jsonData = $('#jsonEditor').val();
        
        if (!levelValue || !fieldName || !jsonData) {
            showToast('提示', '请输入层级数值、字段名称和JSON数据', 'warning');
            return;
        }
        
        //let modelsData;
        //try {
        //    modelsData = JSON.parse(jsonData);
        //} catch (e) {
        //    showToast('错误', 'JSON格式错误: ' + e.message, 'danger');
        //    return;
        //}
        
        $.ajax({
            url: '/module_operate/import_models',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                models_data: jsonData,
                level_value: levelValue,
                field_name: fieldName
            }),
            success: function(res) {
                if (res.success) {
                    showToast('成功', `导入成功: 共${res.result.total}条，导入${res.result.imported}条，已存在${res.result.existed}条`, 'success');
                    table.ajax.reload();
                } else {
                    showToast('错误', '导入失败: ' + res.message, 'danger');
                }
            },
            error: function(err) {
                showToast('错误', '请求失败: ' + (err.responseJSON?.message || err.statusText), 'danger');
            }
        });
    });
    
    // 添加按钮点击
    $('#btnAdd').click(function() {
        $('#modalTitle').text('添加模型');
        $('#modelId').val('');
        $('#modelForm')[0].reset();
        $('#modelIsDeleted').prop('checked', false);
        $('#modelNeedSync').val('0');
        $('#modelModal').modal('show');
    });
    
    // 编辑按钮点击
    $('#btnEdit').click(function() {
        var id = $('.select-item:checked').first().data('id');
        editModel(id);
    });
    
    // 表格中编辑按钮点击
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        editModel(id);
    });
    
    // 编辑模型函数
    function editModel(id) {
        $('#modalTitle').text('编辑模型');
        $('#modelId').val(id);
        
        // 获取行数据
        var rowData = table.row(function(idx, data) {
            return data.id === id;
        }).data();
        
        if (rowData) {
            $('#modelName').val(rowData.model_name);
            $('#modelSortOrder').val(rowData.sort_order);
            $('#modelNeedSync').val(rowData.need_sync || '0');
            $('#modelIsDeleted').prop('checked', rowData.is_deleted === 1);
            $('#modelModal').modal('show');
        } else {
            showToast('错误', '获取数据失败', 'danger');
        }
    }
    
    // 保存模型按钮点击
    $('#btnSaveModel').click(function() {
        const id = $('#modelId').val();
        const data = {
            model_name: $('#modelName').val(),
            sort_order: $('#modelSortOrder').val() ? parseInt($('#modelSortOrder').val()) : null,
            need_sync: parseInt($('#modelNeedSync').val()),
            is_deleted: $('#modelIsDeleted').is(':checked') ? 1 : 0
        };
        
        if (!data.model_name) {
            showToast('提示', '请输入模型名称', 'warning');
            return;
        }
        
        if (id) {
            // 更新
            $.ajax({
                url: `/module_operate/update/${id}`,
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(res) {
                    if (res.success) {
                        $('#modelModal').modal('hide');
                        showToast('成功', '模型更新成功', 'success');
                        table.ajax.reload();
                    } else {
                        showToast('错误', '更新失败: ' + res.message, 'danger');
                    }
                },
                error: function(err) {
                    showToast('错误', '请求失败: ' + (err.responseJSON?.message || err.statusText), 'danger');
                }
            });
        } else {
            // 创建
            $.ajax({
                url: '/module_operate/create',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(res) {
                    if (res.success) {
                        $('#modelModal').modal('hide');
                        showToast('成功', '模型创建成功', 'success');
                        table.ajax.reload();
                    } else {
                        showToast('错误', '创建失败: ' + res.message, 'danger');
                    }
                },
                error: function(err) {
                    showToast('错误', '请求失败: ' + (err.responseJSON?.message || err.statusText), 'danger');
                }
            });
        }
    });
    
    // 删除按钮点击
    $('#btnDelete').click(function() {
        var ids = getSelectedIds();
        if (ids.length === 0) return;
        
        showConfirm('确认删除', `确定要删除这${ids.length}条记录吗？`, function() {
            executeBatchAction(ids, 'delete');
        });
    });
    
    // 表格中删除按钮点击
    $(document).on('click', '.btn-delete', function() {
        var id = $(this).data('id');
        
        showConfirm('确认删除', '确定要删除这条记录吗？', function() {
            $.ajax({
                url: `/module_operate/delete/${id}`,
                type: 'DELETE',
                success: function(res) {
                    if (res.success) {
                        showToast('成功', '记录已删除', 'success');
                        table.ajax.reload();
                    } else {
                        showToast('错误', '删除失败: ' + res.message, 'danger');
                    }
                },
                error: function(err) {
                    showToast('错误', '请求失败: ' + (err.responseJSON?.message || err.statusText), 'danger');
                }
            });
        });
    });
    
    // 表格中同步状态按钮点击
    $(document).on('click', '.btn-sync', function() {
        var id = $(this).data('id');
        var needSync = $(this).data('sync');
        var action = needSync == 1 ? '标记为需要同步' : '标记为无需同步';
        
        showConfirm('确认操作', `确定要${action}吗？`, function() {
            $.ajax({
                url: `/module_operate/sync_status/${id}`,
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    need_sync: needSync
                }),
                success: function(res) {
                    if (res.success) {
                        showToast('成功', '同步状态已更新', 'success');
                        table.ajax.reload();
                    } else {
                        showToast('错误', '更新失败: ' + res.message, 'danger');
                    }
                },
                error: function(err) {
                    showToast('错误', '请求失败: ' + (err.responseJSON?.message || err.statusText), 'danger');
                }
            });
        });
    });
    
    // 置顶按钮点击
    $('#btnTop').click(function() {
        var ids = getSelectedIds();
        if (ids.length === 0) return;
        
        showConfirm('确认操作', `确定要将这${ids.length}条记录置顶吗？`, function() {
            executeBatchAction(ids, 'top');
        });
    });
    
    // 置底按钮点击
    $('#btnBottom').click(function() {
        var ids = getSelectedIds();
        if (ids.length === 0) return;
        
        showConfirm('确认操作', `确定要将这${ids.length}条记录置底吗？`, function() {
            executeBatchAction(ids, 'bottom');
        });
    });
    
    // 同步按钮点击
    $('#btnSync').click(function() {
        var ids = getSelectedIds();
        if (ids.length === 0) return;
        
        showConfirm('确认操作', `确定要将这${ids.length}条记录标记为需要同步吗？`, function() {
            executeBatchAction(ids, 'sync');
        });
    });
    
    // 获取选中的ID
    function getSelectedIds() {
        var ids = [];
        $('.select-item:checked').each(function() {
            ids.push($(this).data('id'));
        });
        return ids;
    }
    
    // 执行批量操作
    function executeBatchAction(ids, action) {
        $.ajax({
            url: '/module_operate/batch',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                ids: ids,
                action: action
            }),
            success: function(res) {
                if (res.success) {
                    var actionText = {
                        'delete': '删除',
                        'top': '置顶',
                        'bottom': '置底',
                        'sync': '标记同步',
                        'unsync': '取消同步'
                    }[action];
                    
                    showToast('成功', `${actionText}操作成功，影响${res.count}条记录`, 'success');
                    table.ajax.reload();
                } else {
                    showToast('错误', `操作失败: ${res.message}`, 'danger');
                }
            },
            error: function(err) {
                showToast('错误', '请求失败: ' + (err.responseJSON?.message || err.statusText), 'danger');
            }
        });
    }
    
    // 显示确认对话框
    function showConfirm(title, message, callback) {
        $('#confirmTitle').text(title);
        $('#confirmMessage').text(message);
        $('#confirmModal').modal('show');
        
        $('#btnConfirm').off('click').on('click', function() {
            $('#confirmModal').modal('hide');
            if (typeof callback === 'function') {
                callback();
            }
        });
    }

    // 绑定自定义分页确认按钮事件
    $(document).on('click', '.apply-custom-length', function() {
        var customLength = parseInt($('.custom-length-input').val());
        if (customLength && customLength > 0) {
            if (customLength > 1000) {
                if (!confirm('加载大量数据可能会影响性能，是否继续？')) {
                    return;
                }
            }
            // 存储到localStorage
            localStorage.setItem('dt_custom_length', customLength);
            table.page.len(customLength).draw();
        }
    });

    // 支持Enter键提交
    $(document).on('keypress', '.custom-length-input', function(e) {
        if (e.which === 13) {
            $('.apply-custom-length').click();
        }
    });

    // 即时验证输入值
    $(document).on('input', '.custom-length-input', function() {
        var value = $(this).val();
        var numValue = parseInt(value);
        if (value && (isNaN(numValue) || numValue < 1)) {
            $(this).addClass('is-invalid');
            $('.apply-custom-length').prop('disabled', true);
        } else {
            $(this).removeClass('is-invalid');
            $('.apply-custom-length').prop('disabled', false);
        }
    });

    // 加载时恢复用户设置
    var savedLength = localStorage.getItem('dt_custom_length');
    if (savedLength) {
        // 在DataTables初始化后应用
        table.on('init.dt', function() {
            setTimeout(function() {
                table.page.len(parseInt(savedLength)).draw();
                $('.custom-length-input').val(savedLength);
            }, 100);
        });
    }

    // 高级筛选功能实现
    // 可用的筛选字段定义
    const filterFields = [
        { id: 'id', name: 'ID', type: 'number' },
        { id: 'model_name', name: '模型名称', type: 'string' },
        { id: 'sort_order', name: '排序顺序', type: 'number' },
        { id: 'need_sync', name: '同步状态', type: 'boolean' },
        { id: 'create_time', name: '创建时间', type: 'date' },
        { id: 'update_time', name: '更新时间', type: 'date' },
        { id: 'create_by', name: '创建人', type: 'string' },
        { id: 'update_by', name: '更新人', type: 'string' },
        { id: 'is_deleted', name: '状态', type: 'boolean' }
    ];
    
    // 筛选运算符定义
    const filterOperators = {
        string: [
            { id: 'eq', name: '等于' },
            { id: 'neq', name: '不等于' },
            { id: 'contains', name: '包含' },
            { id: 'not_contains', name: '不包含' },
            { id: 'starts_with', name: '开头是' },
            { id: 'ends_with', name: '结尾是' },
            { id: 'is_null', name: '是 null' },
            { id: 'is_not_null', name: '不是 null' }
        ],
        number: [
            { id: 'eq', name: '等于' },
            { id: 'neq', name: '不等于' },
            { id: 'gt', name: '大于' },
            { id: 'gte', name: '大于等于' },
            { id: 'lt', name: '小于' },
            { id: 'lte', name: '小于等于' },
            { id: 'is_null', name: '是 null' },
            { id: 'is_not_null', name: '不是 null' }
        ],
        date: [
            { id: 'eq', name: '等于' },
            { id: 'neq', name: '不等于' },
            { id: 'gt', name: '大于' },
            { id: 'gte', name: '大于等于' },
            { id: 'lt', name: '小于' },
            { id: 'lte', name: '小于等于' },
            { id: 'is_null', name: '是 null' },
            { id: 'is_not_null', name: '不是 null' }
        ],
        boolean: [
            { id: 'eq', name: '等于' },
            { id: 'neq', name: '不等于' }
        ]
    };
    
    // 生成唯一ID
    function generateId() {
        return 'filter_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
    }
    
    // 创建字段选择器HTML
    function createFieldSelectHtml() {
        let html = '<select class="form-control form-control-sm filter-field">';
        filterFields.forEach(field => {
            html += `<option value="${field.id}" data-type="${field.type}">${field.name}</option>`;
        });
        html += '</select>';
        return html;
    }
    
    // 创建运算符选择器HTML
    function createOperatorSelectHtml(fieldType) {
        let operators = filterOperators[fieldType] || [];
        let html = '<select class="form-control form-control-sm filter-operator">';
        operators.forEach(op => {
            html += `<option value="${op.id}">${op.name}</option>`;
        });
        html += '</select>';
        return html;
    }
    
    // 创建值输入HTML
    function createValueInputHtml(fieldType, operatorId) {
        // 不需要值输入的操作符
        const noValueOperators = ['is_null', 'is_not_null'];
        
        if (noValueOperators.includes(operatorId)) {
            return '';
        }
        
        let html = '';
        switch (fieldType) {
            case 'string':
                html = '<input type="text" class="form-control form-control-sm filter-value">';
                break;
            case 'number':
                html = '<input type="number" class="form-control form-control-sm filter-value">';
                break;
            case 'date':
                html = '<input type="datetime-local" class="form-control form-control-sm filter-value">';
                break;
            case 'boolean':
                html = '<select class="form-control form-control-sm filter-value">' +
                       '<option value="true">是</option>' +
                       '<option value="false">否</option>' +
                       '</select>';
                break;
            default:
                html = '<input type="text" class="form-control form-control-sm filter-value">';
        }
        return html;
    }
    
    // 绑定条件事件
    function bindFilterEvents(condition) {
        // 字段变更事件
        condition.find('.filter-field').on('change', function() {
            const fieldType = $(this).find('option:selected').data('type');
            const operatorContainer = condition.find('.filter-operator-container');
            const valueContainer = condition.find('.filter-value-container');
            
            // 更新运算符选择器
            operatorContainer.html(createOperatorSelectHtml(fieldType));
            
            // 获取选中的运算符
            const operatorId = condition.find('.filter-operator').val();
            
            // 更新值输入框
            valueContainer.html(createValueInputHtml(fieldType, operatorId));
        });
        
        // 运算符变更事件
        condition.find('.filter-operator-container').on('change', '.filter-operator', function() {
            const fieldType = condition.find('.filter-field option:selected').data('type');
            const operatorId = $(this).val();
            const valueContainer = condition.find('.filter-value-container');
            
            // 更新值输入框
            valueContainer.html(createValueInputHtml(fieldType, operatorId));
        });
        
        // 删除条件按钮 (修正逻辑)
        condition.find('.btn-remove-condition').on('click', function() {
            const conditionWrapper = $(this).closest('.filter-condition-wrapper'); // Use wrapper

            // 如果条件前面有关系符，删除它
            const prevRelation = conditionWrapper.prev('.condition-relation');
            if (prevRelation.length) {
                prevRelation.remove();
            }
            // 如果条件是第一个，但后面有关系符，删除后面的关系符
            else if (conditionWrapper.next('.condition-relation').length) {
                 condition.next('.condition-relation').remove();
            }

            // 删除条件及其包装器
            conditionWrapper.remove();
        });
    }
    
    // 添加条件
    function addCondition(container) {
        return addFilterCondition(container, null, null, null);
    }
    
    // 添加嵌套组
    function addNestedGroup(container) {
        return addFilterGroup(container, 'AND');
    }
    
    // 填充操作符下拉框
    function populateOperators(fieldSelect, operatorSelect) {
        const fieldType = fieldSelect.find('option:selected').data('type');
        const operators = filterOperators[fieldType] || [];
        
        operatorSelect.empty();
        operators.forEach(op => {
            operatorSelect.append(`<option value="${op.id}">${op.name}</option>`);
        });
    }
    
    // 添加筛选条件 (重构 HTML 插入逻辑)
    function addFilterCondition(container, field, operator, value) {
        const hasExistingItems = container.children().length > 0;
        const conditionId = generateId();
        const fieldObj = filterFields.find(f => f.id === field) || filterFields[0];
        const fieldType = fieldObj ? fieldObj.type : 'string';
        const operatorId = operator || (filterOperators[fieldType][0] ? filterOperators[fieldType][0].id : 'eq');

        // 1. Add relation indicator BEFORE the item if not the first item
        if (hasExistingItems) {
            const relationId = generateId();
            // Get parent group's operator to set the initial text correctly
            const internalOperator = container.closest('.filter-group').data('internal-operator') || 'AND';
            const relationHtml = `
                <div class="condition-relation text-center my-2" data-relation-id="${relationId}">
                    <span class="badge badge-secondary group-relation-indicator">${internalOperator}</span>
                </div>
            `;
            container.append(relationHtml);
        }

        // 2. Create and append the condition item wrapper
        let conditionHtml = `
            <div class="filter-condition-wrapper" data-wrapper-id="${conditionId}">
                <div class="filter-condition d-flex align-items-center mb-2" data-condition-id="${conditionId}">
                    <div class="filter-field-container mr-2">
                        ${createFieldSelectHtml()}
                    </div>
                    <div class="filter-operator-container mr-2">
                        ${createOperatorSelectHtml(fieldType)}
                    </div>
                    <div class="filter-value-container mr-2">
                        ${createValueInputHtml(fieldType, operatorId)}
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-sm btn-danger btn-remove-condition" title="删除条件">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        container.append(conditionHtml);

        const conditionElement = container.find(`.filter-condition[data-condition-id="${conditionId}"]`);

        // 设置初始值
        if (field) {
            conditionElement.find('.filter-field').val(field);
        }
        if (operator) {
            conditionElement.find('.filter-operator').val(operator);
        }
        if (value !== undefined && value !== null) {
            if (fieldType === 'boolean') {
                conditionElement.find('.filter-value').val(value === true || value === 'true' ? 'true' : 'false');
            } else if (fieldType === 'date' && typeof value === 'string') {
                 if (value) {
                    try {
                        // Assuming value is ISO string or similar, format for datetime-local
                        const date = new Date(value);
                        // Format to YYYY-MM-DDTHH:mm
                        const formattedValue = date.getFullYear() + '-' +
                                           ('0' + (date.getMonth() + 1)).slice(-2) + '-' +
                                           ('0' + date.getDate()).slice(-2) + 'T' +
                                           ('0' + date.getHours()).slice(-2) + ':' +
                                           ('0' + date.getMinutes()).slice(-2);
                         conditionElement.find('.filter-value').val(formattedValue);
                         console.log(`Formatted date for input: ${formattedValue}`);
                    } catch (e) {
                        console.warn("Could not format date for input:", value, e);
                        conditionElement.find('.filter-value').val(value); // Use original if formatting fails
                    }
                } else {
                     conditionElement.find('.filter-value').val(''); // Set empty for null/undefined date
                }
            } else {
                 conditionElement.find('.filter-value').val(value);
            }
        }


        // 绑定事件
        bindFilterEvents(conditionElement);

        // updateGroupRelationIndicators is called when group operator changes, not needed here.

        return conditionElement;
    }
    
    // 添加条件组 (重构 HTML 插入逻辑)
    function addFilterGroup(container) {
        const hasExistingItems = container.children().length > 0;
        const groupId = generateId();

        // 1. Add relation indicator BEFORE the item if not the first item
        if (hasExistingItems) {
            const relationId = generateId();
            // Get parent group's operator to set the initial text correctly
            const internalOperator = container.closest('.filter-group').data('internal-operator') || 'AND';
            const relationHtml = `
                <div class="condition-relation text-center my-2" data-relation-id="${relationId}">
                    <span class="badge badge-secondary group-relation-indicator">${internalOperator}</span>
                </div>
            `;
            container.append(relationHtml);
        }

        // 2. Create and append the group item wrapper
        const groupHtml = `
            <div class="filter-group-wrapper" data-wrapper-id="${groupId}">
                <div class="filter-group mb-3" data-group-id="${groupId}" data-internal-operator="AND">
                    <div class="filter-group-header d-flex align-items-center justify-content-between">
                        <div>
                            <span class="group-label mr-2 font-weight-bold">嵌套条件组</span>
                            <div class="filter-group-operator btn-group btn-group-sm" role="group">
                                <span class="mr-1 text-muted small align-self-center">组内逻辑:</span>
                                <button type="button" class="btn btn-outline-secondary active group-op-and">AND</button>
                                <button type="button" class="btn btn-outline-secondary group-op-or">OR</button>
                            </div>
                        </div>
                        <div class="filter-group-actions">
                            <button class="btn btn-sm btn-primary btn-add-group-condition" title="添加条件">
                                <i class="fas fa-plus"></i> 条件
                            </button>
                            <button class="btn btn-sm btn-secondary btn-add-nested-group" title="添加嵌套组">
                                <i class="fas fa-layer-group"></i> 嵌套组
                            </button>
                            <button class="btn btn-sm btn-danger btn-remove-group" title="删除此组">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="filter-conditions pl-3 mt-2">
                        <!-- Conditions will be added here -->
                    </div>
                </div>
            </div>
        `;
        container.append(groupHtml);
        const group = container.find(`.filter-group[data-group-id="${groupId}"]`);
        bindGroupEvents(group);

        // 默认添加一个空条件到新组
        addFilterCondition(group.find('.filter-conditions'), null, null, null);

        return group;
    }
    
    // 绑定条件组事件
    function bindGroupEvents(group) {
        const conditionsContainer = group.find('> .filter-conditions');

        // 绑定组内操作符切换事件
        group.find('.group-op-and').on('click', function(e) {
            e.stopPropagation();
            if (!$(this).hasClass('active')) {
                $(this).addClass('active');
                group.find('.group-op-or').removeClass('active');
                group.data('internal-operator', 'AND');
                 updateGroupRelationIndicators(group); // Update indicators within this group
            }
        });
        group.find('.group-op-or').on('click', function(e) {
            e.stopPropagation();
            if (!$(this).hasClass('active')) {
                $(this).addClass('active');
                group.find('.group-op-and').removeClass('active');
                group.data('internal-operator', 'OR');
                 updateGroupRelationIndicators(group); // Update indicators within this group
            }
        });

        // 添加条件按钮
        group.find('.btn-add-group-condition').on('click', function(e) {
            e.stopPropagation();
            addFilterCondition(conditionsContainer, null, null, null);
        });

        // 添加嵌套组按钮
        group.find('.btn-add-nested-group').on('click', function(e) {
            e.stopPropagation();
            addFilterGroup(conditionsContainer); // Add nested group
        });

        // 删除组按钮 (重构移除逻辑)
        group.find('.btn-remove-group').on('click', function(e) {
            e.stopPropagation();
            const groupWrapper = $(this).closest('.filter-group-wrapper');
            const prevElement = groupWrapper.prev();
            const nextElement = groupWrapper.next();

            // If there's a relation indicator immediately before this group, remove it.
            if (prevElement.hasClass('condition-relation')) {
                prevElement.remove();
            }
            // If this was the first item, but there's a relation indicator immediately after, remove that one.
            else if (nextElement.hasClass('condition-relation')) {
                nextElement.remove();
            }

            // Remove the group wrapper itself
            groupWrapper.remove();

            // Special case: If removing the last top-level group, remove any preceding top-level relation as well.
            if ($('#topLevelGroups > .filter-group-wrapper').length === 0) {
                $('#topLevelGroups > .condition-relation.top-level-relation').last().remove();
            }
        });
    }
    
    // 更新组内条件/子组之间的关系指示符 (选择器应已正确)
    function updateGroupRelationIndicators(group) {
        if (!group || group.length === 0) return; // Exit if group is invalid
        const internalOperator = group.data('internal-operator') || 'AND';
        // Ensure we only update the relation indicators directly under this group's conditions container
        group.find('> .filter-conditions > .condition-relation .group-relation-indicator').text(internalOperator);
        // This should NOT affect nested groups because the selector uses direct child > filter-conditions > relation
    }
    
    // 获取筛选条件的JSON表示 (重构顶层操作符获取)
    function getFilterJson() {
        // Read the operator from the FIRST top-level relation if it exists
        // const firstRelation = $('#topLevelGroups > .condition-relation.top-level-relation').first();
        // If there are multiple groups, the operator is defined by the relations *between* them.
        // If only one group, the concept of a root operator doesn't strictly apply in the same way, default to AND.
        // const rootOperator = $('#topLevelGroups > .filter-group-wrapper').length > 1
        //                      ? (firstRelation.length ? firstRelation.data('operator') : 'AND')
        //                      : 'AND';

        const groups = [];
        const operators = [];

        // Iterate through ALL direct children of #topLevelGroups
        $('#topLevelGroups > *').each(function() {
            const element = $(this);
            if (element.hasClass('filter-group-wrapper')) {
                // Found a group, process it
                const groupElement = element.find('.filter-group').first();
                if (groupElement.length) {
                    const groupData = getGroupJson(groupElement);
                    // Only add group if it contains valid conditions or nested groups
                    let isEmptyGroup = true;
                     if (groupData.conditions && groupData.conditions.length > 0) {
                          for(const cond of groupData.conditions) {
                               if (cond.type === 'group' || (cond.type === 'condition' && cond.field)) {
                                    isEmptyGroup = false;
                                    break;
                               }
                          }
                     }
                    if (!isEmptyGroup) {
                         console.log(`[getFilterJson] Adding valid group ${groupElement.data('group-id')} to groups list.`);
                         groups.push(groupData);
                    } else {
                         console.log(`[getFilterJson] Skipping empty group ${groupElement.data('group-id')}.`);
                    }
                } else {
                     console.warn('[getFilterJson] Wrapper found without .filter-group inside?');
                }
            } else if (element.hasClass('condition-relation') && element.hasClass('top-level-relation')) {
                // Found a top-level operator between groups
                const operator = element.data('operator') || 'AND'; // Default to AND if data missing
                console.log(`[getFilterJson] Found top-level operator: ${operator}`);
                operators.push(operator);
            }
            // Ignore other element types if any
        });


        const result = {
            // operator: rootOperator, // Remove single root operator
            groups: groups,       // Array of group objects
            operators: operators  // Array of operators BETWEEN groups (length should be groups.length - 1)
        };

        // Validation check (optional but recommended)
        if (groups.length > 1 && operators.length !== groups.length - 1) {
            console.warn(`[getFilterJson] Mismatch detected: ${groups.length} groups found, but ${operators.length} operators found between them.`);
            // Decide on fallback behavior - maybe default all operators to AND?
            // For now, just log the warning. Backend should handle this too.
        }

        console.log('Generated Filter JSON:', JSON.stringify(result, null, 2));
        return result;
    }
    
    // 获取条件组的JSON表示
    function getGroupJson(group) {
        const groupOperator = group.data('internal-operator') || 'AND';
        const conditions = [];

        const conditionsContainer = group.find('> .filter-conditions'); // Get the direct container

        console.log(`[getGroupJson] >>> ENTER Group ID: ${group.data('group-id')}, Internal Operator: ${groupOperator}`);

        // Iterate COMBINED wrappers and relation indicators to process in order
        conditionsContainer.children().each(function() {
            const element = $(this);

            if (element.hasClass('filter-condition-wrapper')) {
                const conditionElement = element.find('.filter-condition').first();
                if (conditionElement.length > 0) {
                    const fieldSelect = conditionElement.find('.filter-field');
                    const operatorSelect = conditionElement.find('.filter-operator');
                    const valueInput = conditionElement.find('.filter-value');

                    const field = fieldSelect.val();
                    const operator = operatorSelect.val();
                    let rawValue = valueInput.val();

                    const fieldType = fieldSelect.find('option:selected').data('type');

                    console.log(`[getGroupJson]    Found Condition: Field=${field}, Op=${operator}, RawValue=${rawValue}, Type=${fieldType}`);

                    if (field && operator) {
                        const conditionData = {
                            type: 'condition',
                            field: field,
                            operator: operator
                        };

                        if (operator !== 'is_null' && operator !== 'is_not_null') {
                             let value = rawValue;
                             if (rawValue !== null && rawValue !== undefined && rawValue !== '') { // Only process if value is present and not empty string
                                try { // Wrap conversion in try-catch
                                    if (fieldType === 'number') {
                                        value = Number(rawValue);
                                        if (isNaN(value)) { // Check if conversion failed
                                            console.warn(`[getGroupJson]      Could not parse number: '${rawValue}', setting value to null.`);
                                            value = null;
                                        }
                                    } else if (fieldType === 'boolean') {
                                        value = rawValue === 'true';
                                    } else if (fieldType === 'date') {
                                        if (rawValue) {
                                            let dtStr = rawValue;
                                            if (dtStr.length === 16) dtStr += ':00';
                                            value = new Date(dtStr).toISOString();
                                            console.log(`[getGroupJson]      Formatted date value: ${value}`);
                                        } else {
                                            value = null;
                                        }
                                    }
                                } catch (e) {
                                     console.error(`[getGroupJson]      Error processing value '${rawValue}' for field '${field}' (type: ${fieldType}): ${e}. Setting value to null.`);
                                     value = null; // Set to null on error
                                }
                            } else {
                                // Treat empty string or null/undefined as null unless operator is 'is_null'/'is_not_null'
                                value = null;
                            }
                            conditionData.value = value;
                            console.log(`[getGroupJson]      Processed Value:`, value);
                        }
                         // Only add if the condition has a field selected
                         if (conditionData.field) {
                            console.log(`[getGroupJson]    ==> Adding Condition Data:`, JSON.parse(JSON.stringify(conditionData)));
                            conditions.push(conditionData);
                         } else {
                             console.log(`[getGroupJson]    Skipping condition with no field selected.`);
                         }
                    } else {
                         console.log(`[getGroupJson]    Skipping incomplete condition (missing field or operator).`);
                    }
                } else {
                    console.warn(`[getGroupJson]    Condition wrapper found, but no .filter-condition inside?`, element[0]);
                }
            } else if (element.hasClass('filter-group-wrapper')) {
                const groupElement = element.find('.filter-group').first();
                if (groupElement.length > 0) {
                    const nestedGroupId = groupElement.data('group-id');
                    console.log(`[getGroupJson]    Found Nested Group Wrapper, Target Group ID: ${nestedGroupId}`);
                    console.log(`[getGroupJson]    >>> Calling getGroupJson recursively for nested group ${nestedGroupId}...`);
                    const nestedGroup = getGroupJson(groupElement); // Recursive call
                    console.log(`[getGroupJson]    <<< Recursive call for ${nestedGroupId} returned:`, JSON.parse(JSON.stringify(nestedGroup)));

                     // Check if nestedGroup itself is valid and has non-empty conditions
                     let isEmptyNestedGroup = true;
                     if (nestedGroup && nestedGroup.conditions && nestedGroup.conditions.length > 0) {
                         for (const cond of nestedGroup.conditions) {
                             if (cond.type === 'group' || (cond.type === 'condition' && cond.field)) {
                                 isEmptyNestedGroup = false;
                                 break;
                             }
                         }
                     }

                    if (!isEmptyNestedGroup) {
                        nestedGroup.type = 'group'; // Ensure type is set
                        console.log(`[getGroupJson]    ==> Adding Nested Group Data:`, JSON.parse(JSON.stringify(nestedGroup)));
                        conditions.push(nestedGroup);
                    } else {
                        console.log(`[getGroupJson]    Skipping empty or invalid nested group ${nestedGroupId}.`);
                    }
                } else {
                     console.warn(`[getGroupJson]    Group wrapper found, but no .filter-group inside?`, element[0]);
                }
            } else if (element.hasClass('condition-relation')) {
                // We don't need to process relation indicators when building the JSON structure
                console.log(`[getGroupJson]  -- Skipping relation element:`, element[0]);
            } else {
                console.log(`[getGroupJson]  -- Found unexpected child type:`, element[0]);
            }
        });

        const result = {
            operator: groupOperator,
            conditions: conditions
        };
        console.log(`[getGroupJson] <<< EXIT Group ID: ${group.data('group-id')}. Result:`, JSON.parse(JSON.stringify(result)));
        return result;
    }


    // 从JSON中设置筛选条件 (重构)
    function setFilterFromJson(json) {
        console.log("[setFilterFromJson] Setting filter from JSON:", JSON.parse(JSON.stringify(json)));
        $('#topLevelGroups').empty(); // Clear existing filters first

        if (!json || !json.groups || json.groups.length === 0) {
            console.log("[setFilterFromJson] No valid groups in JSON, adding default group.");
            addTopLevelGroup(); // Add one default group if JSON is empty/invalid
            return;
        }

        const topOperator = json.operator || 'AND';
        console.log(`[setFilterFromJson] Top Level Operator: ${topOperator}`);

        json.groups.forEach((groupJson, index) => {
            console.log(`[setFilterFromJson] Processing Top Level Group ${index}`);

            // Add relation between top-level groups if needed
            if (index > 0) {
                 // Use the operator from the *overall* JSON structure for relations *between* top-level groups
                 addTopLevelRelation(topOperator);
            }

            // Add the top-level group UI element *without* its default condition initially
            const newGroup = addTopLevelGroup(true); // Pass true to prevent adding default condition
            console.log(`[setFilterFromJson] Setting data for Top Level Group ${newGroup.data('group-id')} from JSON:`, JSON.parse(JSON.stringify(groupJson)));
            setGroupFromJson(newGroup, groupJson); // Populate the new group from JSON data
        });

        // Final check: if somehow no groups were added (e.g., all groups in JSON were empty), add a default one.
        if ($('#topLevelGroups > .filter-group-wrapper').length === 0) {
             console.log("[setFilterFromJson] No groups were actually added from JSON, adding default group.");
             addTopLevelGroup();
         }
    }


    // Helper to set a group (top-level or nested) from JSON (重构)
    function setGroupFromJson(groupElement, groupJson) {
        const groupId = groupElement.data('group-id');
        console.log(`[setGroupFromJson] >> Setting Group ID: ${groupId}`);
        const internalOperator = groupJson.operator || 'AND';
        const conditionsContainer = groupElement.find('> .filter-conditions');
        conditionsContainer.empty(); // Clear any potential default condition added previously

        // Set internal operator buttons
        groupElement.data('internal-operator', internalOperator);
        console.log(`[setGroupFromJson]  Setting internal operator to: ${internalOperator}`);
        if (internalOperator === 'OR') {
             groupElement.find('.group-op-or').addClass('active');
             groupElement.find('.group-op-and').removeClass('active');
        } else {
             groupElement.find('.group-op-and').addClass('active');
             groupElement.find('.group-op-or').removeClass('active');
        }

        // Process conditions from JSON
        if (groupJson.conditions && groupJson.conditions.length > 0) {
            console.log(`[setGroupFromJson]  Processing ${groupJson.conditions.length} conditions/groups for group ${groupId}`);
            groupJson.conditions.forEach((conditionJson, index) => {
                // Add relation separator before items 2 onwards
                // Note: The relation indicator text will be set correctly by addFilterCondition/addFilterGroup based on the parent group's operator
                // if (index > 0) {
                //     const relationId = generateId();
                //     const relationHtml = `
                //         <div class="condition-relation text-center my-2" data-relation-id="${relationId}">
                //             <span class="badge badge-secondary group-relation-indicator">${internalOperator}</span>
                //         </div>
                //     `;
                //    conditionsContainer.append(relationHtml);
                // } // This is now handled by the add functions themselves

                // Add the condition or nested group
                if (conditionJson.type === 'condition') {
                    console.log(`[setGroupFromJson]   Adding condition ${index}:`, JSON.parse(JSON.stringify(conditionJson)));
                    addFilterCondition(conditionsContainer, conditionJson.field, conditionJson.operator, conditionJson.value);
                } else if (conditionJson.type === 'group') {
                    console.log(`[setGroupFromJson]   Adding nested group ${index}:`, JSON.parse(JSON.stringify(conditionJson)));
                    const newNestedGroup = addFilterGroup(conditionsContainer); // Add the UI element
                    setGroupFromJson(newNestedGroup, conditionJson); // Recursively populate it
                }
            });
        } else {
            // If a group in the JSON has no conditions, add a default empty one for usability
            console.log(`[setGroupFromJson]  Group ${groupId} has no conditions in JSON, adding default.`);
            addFilterCondition(conditionsContainer, null, null, null);
        }

        // Update visual indicators after populating
        updateGroupRelationIndicators(groupElement); // Ensure indicators match the group op
        console.log(`[setGroupFromJson] << Finished Setting Group ID: ${groupId}`);
    }


    // 添加顶层条件组 (重构)
    function addTopLevelGroup(preventAddingDefaultCondition = false) {
        console.log(`[addTopLevelGroup] Adding new top level group... Prevent default condition: ${preventAddingDefaultCondition}`);
        const groupId = generateId();
        const groupCount = $('#topLevelGroups > .filter-group-wrapper').length;

        // Add relation selector before adding the new group if it's not the first one
        // This is now handled separately by addTopLevelRelation called before this function

        const groupHtml = `
             <div class="filter-group-wrapper" data-wrapper-id="${groupId}">
                <div class="filter-group mb-3" data-group-id="${groupId}" data-internal-operator="AND">
                    <div class="filter-group-header d-flex align-items-center justify-content-between">
                        <div>
                            <span class="group-label mr-2 font-weight-bold">条件组 ${groupCount + 1}</span>
                            <div class="filter-group-operator btn-group btn-group-sm" role="group">
                                <span class="mr-1 text-muted small align-self-center">组内逻辑:</span>
                                <button type="button" class="btn btn-outline-secondary active group-op-and">AND</button>
                                <button type="button" class="btn btn-outline-secondary group-op-or">OR</button>
                            </div>
                        </div>
                        <div class="filter-group-actions">
                            <button class="btn btn-sm btn-primary btn-add-group-condition" title="添加条件">
                                <i class="fas fa-plus"></i> 条件
                            </button>
                            <button class="btn btn-sm btn-secondary btn-add-nested-group" title="添加嵌套组">
                                <i class="fas fa-layer-group"></i> 嵌套组
                            </button>
                            <button class="btn btn-sm btn-danger btn-remove-group" title="删除此组">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="filter-conditions pl-3 mt-2">
                        <!-- Conditions will be added here -->
                    </div>
                </div>
            </div>
        `;

        console.log(`[addTopLevelGroup] Appending group HTML for ID: ${groupId}`);
        $('#topLevelGroups').append(groupHtml);
        const newGroup = $(`#topLevelGroups .filter-group[data-group-id="${groupId}"]`);
        bindGroupEvents(newGroup); // Bind events to the new group

        // Add a default condition only if not prevented (e.g., when loading from JSON)
        if (!preventAddingDefaultCondition) {
            console.log(`[addTopLevelGroup] Adding default condition to group ${groupId}`);
            addFilterCondition(newGroup.find('.filter-conditions'), null, null, null);
        }

        return newGroup; // Return the newly created group element
    }


    // Helper to add relation between top-level groups (重构)
    function addTopLevelRelation(operator = 'AND') {
        console.log(`[addTopLevelRelation] Adding relation with operator: ${operator}`);
        const relationId = generateId();
        // Note: top-level relation uses buttons, not the text span indicator
        const relationHtml = `
           <div class="condition-relation text-center my-2 top-level-relation" data-relation-id="${relationId}" data-operator="${operator}">
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary ${operator === 'AND' ? 'active' : ''} relation-and">AND</button>
                <button type="button" class="btn btn-outline-primary ${operator === 'OR' ? 'active' : ''} relation-or">OR</button>
            </div>
        </div>
     `;
        $('#topLevelGroups').append(relationHtml);

        // Bind events to the new relation buttons
        const relationElement = $(`#topLevelGroups .condition-relation[data-relation-id="${relationId}"]`);
        relationElement.find('.relation-and').on('click', function() {
            if (!$(this).hasClass('active')) {
                $(this).addClass('active').siblings().removeClass('active');
                relationElement.data('operator', 'AND');
            }
        });
        relationElement.find('.relation-or').on('click', function() {
             if (!$(this).hasClass('active')) {
                $(this).addClass('active').siblings().removeClass('active');
                relationElement.data('operator', 'OR');
             }
        });
    }

    // 初始化高级筛选功能 (重构)
    function initAdvancedFilter() {
        console.log("[initAdvancedFilter] Initializing advanced filter...");
        // Add Top Level Group Button
        $('#btnAddTopLevelGroup').on('click', function() {
            // Add relation first if needed
             if ($('#topLevelGroups > .filter-group-wrapper').length > 0) {
                 const lastRelation = $('#topLevelGroups > .condition-relation.top-level-relation').last();
                 const op = lastRelation.length ? lastRelation.data('operator') : 'AND';
                 addTopLevelRelation(op);
             }
            addTopLevelGroup(); // Then add the group
        });

        // Test Button (重构 SQL 预览逻辑)
        $('#btnTestFilter').on('click', function() {
            console.log("[btnTestFilter] Clicked. Getting Filter JSON...");
            const filterJson = getFilterJson();
            console.log("[btnTestFilter] Filter JSON obtained:", JSON.stringify(filterJson, null, 2));

            // 构建SQL预览
            let sqlPreview = 'SELECT * FROM llm_ai_operate WHERE ';

            // Process top-level groups using the new structure
            if (filterJson.groups && filterJson.groups.length > 0) {
                 const groupSqlParts = filterJson.groups.map(groupJson => buildSqlPreviewForGroup(groupJson));
 
                 if (groupSqlParts.length === 1) {
                     // Only one group, just append its conditions (remove outer parens if buildSqlPreviewForGroup adds them unnecessarily for single group)
                     let singleGroupSql = groupSqlParts[0];
                     // Basic check to remove potentially redundant outer parens if group is simple
                     if (singleGroupSql.startsWith('(') && singleGroupSql.endsWith(')')) {
                         // More robust check might be needed depending on buildSqlPreviewForGroup's output
                         // sqlPreview += singleGroupSql.substring(1, singleGroupSql.length - 1);
                         sqlPreview += singleGroupSql; // Keep parens for safety/consistency for now
                     } else {
                         sqlPreview += singleGroupSql;
                     }
                 } else if (groupSqlParts.length > 1) {
                     // Multiple groups, combine using operators
                     const operators = filterJson.operators || [];
                     if (operators.length === groupSqlParts.length - 1) {
                         let combinedSql = groupSqlParts[0];
                         for (let i = 0; i < operators.length; i++) {
                             combinedSql += ` ${operators[i].toUpperCase()} ${groupSqlParts[i+1]}`;
                         }
                         sqlPreview += combinedSql;
                     } else {
                         // Operator count mismatch, fallback to AND
                         console.warn("Operator count mismatch in preview, defaulting to AND.");
                         sqlPreview += groupSqlParts.join(' AND ');
                     }
                 } else {
                     // No valid group SQL parts generated
                     sqlPreview += '1=1';
                 }

                 // Ensure WHERE clause is meaningful (handle empty result)
                 if (sqlPreview.endsWith('WHERE ')) {
                     sqlPreview += '1=1';
                 }
            } else {
                sqlPreview += '1=1'; // No conditions
            }

            console.log("SQL Preview:", sqlPreview);
            alert('筛选条件 JSON 和 SQL 预览已打印到控制台 (F12)');
        });

        // 构建 SQL 预览 (重构 - 接收 JSON 对象)
        function buildSqlPreviewForGroup(groupJson) {
            let sql = '';
            const groupOperator = groupJson.operator || 'AND'; // Default to AND

            if (groupJson.conditions && groupJson.conditions.length > 0) {
                const conditionSqlParts = groupJson.conditions.map(conditionJson => {
                    if (conditionJson.type === 'condition') {
                        return buildSqlPreviewForCondition(conditionJson);
                    } else if (conditionJson.type === 'group') {
                        return buildSqlPreviewForGroup(conditionJson); // Recursive call
                    }
                    return ''; // Should not happen
                }).filter(part => part !== '' && part !== '(1=0)'); // Filter out empty strings and always-false parts

                if (conditionSqlParts.length > 0) {
                     // Join parts with the group's internal operator and wrap in parentheses
                     sql = `(${conditionSqlParts.join(` ${groupOperator} `)})`;
                } else {
                     sql = '(1=1)'; // Empty group evaluates to true if all conditions were invalid/empty
                }
            } else {
                sql = '(1=1)'; // Empty group
            }
            return sql;
        }

         // Helper to build SQL for a single condition from JSON
         function buildSqlPreviewForCondition(conditionJson) {
             let fieldSql = '';
             const field = conditionJson.field;
             const operator = conditionJson.operator;
             const value = conditionJson.value; // Can be null

             // Skip if field or operator is missing
             if (!field || !operator) {
                 console.warn("Skipping condition due to missing field or operator:", conditionJson);
                 return '';
             }

             // Handle operators that don't need a value
             if (operator === 'is_null') return `${field} IS NULL`;
             if (operator === 'is_not_null') return `${field} IS NOT NULL`;

             // For other operators, check if value is null/undefined
             if (value === null || value === undefined) {
                  // Operators like equals/not equals can compare with NULL
                  if (operator === 'eq') return `${field} IS NULL`; // SQL standard for NULL comparison
                  if (operator === 'neq') return `${field} IS NOT NULL`; // SQL standard for NULL comparison
                  // Other operators like >, <, contains with NULL are generally meaningless or false
                  console.warn(`Operator ${operator} used with null/undefined value for field ${field}. SQL might be invalid or yield no results.`);
                  return '(1=0)'; // Return a condition that is always false
             }


             // Find field type (needed for formatting)
             const fieldObj = filterFields.find(f => f.id === field);
             const fieldType = fieldObj ? fieldObj.type : 'string';

             const formatValue = (val, fType) => {
                 // Value is guaranteed not null/undefined here by checks above
                 switch (fType) {
                     case 'number': return Number(val); // Ensure it's a number
                     case 'boolean': return (val === true || String(val).toLowerCase() === 'true') ? 1 : 0; // Use 1/0 for boolean in SQL
                     case 'date': // Assuming ISO string from JSON
                          if (typeof val === 'string') {
                               // Basic format for SQL datetime/date - might need adjustment for specific DB
                               return `'${val.replace('T', ' ').split('.')[0].split('+')[0]}'`;
                          }
                          return `'${val}'`; // Fallback
                     default: // String
                          // Escape single quotes for SQL
                          return `'${String(val).replace(/'/g, "''")}'`;
                 }
             };

             const formattedVal = formatValue(value, fieldType);

             switch (operator) {
                 case 'eq': fieldSql = `${field} = ${formattedVal}`; break;
                 case 'neq': fieldSql = `${field} != ${formattedVal}`; break; // Or <> depending on DB
                 case 'gt': fieldSql = `${field} > ${formattedVal}`; break;
                 case 'gte': fieldSql = `${field} >= ${formattedVal}`; break;
                 case 'lt': fieldSql = `${field} < ${formattedVal}`; break;
                 case 'lte': fieldSql = `${field} <= ${formattedVal}`; break;
                 case 'contains': fieldSql = `${field} LIKE '%${String(value).replace(/'/g, "''")}%'`; break; // Use raw value for LIKE pattern
                 case 'not_contains': fieldSql = `${field} NOT LIKE '%${String(value).replace(/'/g, "''")}%'`; break;
                 case 'starts_with': fieldSql = `${field} LIKE '${String(value).replace(/'/g, "''")}%'`; break;
                 case 'ends_with': fieldSql = `${field} LIKE '%${String(value).replace(/'/g, "''")}'`; break;
                 // is_null and is_not_null handled earlier
                 default:
                      console.warn(`Unsupported operator in SQL preview: ${operator}`);
                      fieldSql = '(1=1)'; // Default to true for unknown operator? Or false? Let's use true to not break query.
             }

             return fieldSql;
         }

        // Apply Filter Button (重构)
        $('#btnApplyAdvancedFilter').on('click', function() {
            console.log("[btnApplyAdvancedFilter] Clicked. Getting Filter JSON...");
            const filterJson = getFilterJson();
            console.log("[btnApplyAdvancedFilter] Filter JSON obtained:", JSON.parse(JSON.stringify(filterJson)));

             // Basic validation: Check if there's at least one group with valid conditions
             let hasConditions = false;
             if (filterJson.groups && filterJson.groups.length > 0) {
                 hasConditions = true; // getFilterJson already filters out empty groups
             }

            if (hasConditions) {
                console.log("[btnApplyAdvancedFilter] Saving valid filter to localStorage.");
                localStorage.setItem('advanced_filter', JSON.stringify(filterJson));
            } else {
                 localStorage.removeItem('advanced_filter'); // Remove filter if no valid conditions
                 console.log("[btnApplyAdvancedFilter] No valid conditions found, removing saved filter.");
                 // Optionally provide feedback to user that filter is empty/invalid
                 // showToast('提示', '筛选条件为空或无效，未应用筛选。', 'info');
            }

            // 重新加载表格数据
            console.log("[btnApplyAdvancedFilter] Reloading DataTable...");
            table.ajax.reload(null, false); // Reload data, false means don't reset pagination
        });

        // Reset Filter Button
        $('#btnResetAdvancedFilter').on('click', function() {
            console.log("[btnResetAdvancedFilter] Clicked.");
            // 清空所有条件组
            $('#topLevelGroups').empty();
            // Add back a single default group
            addTopLevelGroup(); // Adds the group and a default condition
            // Optionally clear localStorage and reload table
            localStorage.removeItem('advanced_filter');
            table.ajax.reload(null, false);
            showToast('提示', '筛选条件已重置。', 'info');
        });

        // Load saved filter on initialization
        const savedFilter = localStorage.getItem('advanced_filter');
        if (savedFilter) {
            console.log("[initAdvancedFilter] Found saved filter in localStorage.");
            try {
                const filterJson = JSON.parse(savedFilter);
                setFilterFromJson(filterJson);
                console.log("Loaded saved filter from localStorage.");
            } catch (e) {
                console.error('Failed to parse saved filter:', e);
                console.log("[initAdvancedFilter] Error parsing saved filter, loading default.");
                localStorage.removeItem('advanced_filter'); // Remove corrupted filter
                addTopLevelGroup(); // Load default
            }
        } else {
            console.log('[initAdvancedFilter] No saved filter found in localStorage, loading default group.');
            // 如果没有保存的条件，添加第一个条件组
            addTopLevelGroup();
        }
    }

    // 在页面加载完成后初始化高级筛选功能
    initAdvancedFilter();
});
</script>
{% endblock %}