from flask import Blueprint, render_template, request, jsonify, current_app
import json
import traceback
from app.services.business.base_service import BaseService # 确保导入BaseService用于类型检查

def create_datatable_blueprint(module_name, import_name, service_instance, config):
    """
    创建并配置一个用于数据表格管理的Flask Blueprint。

    Args:
        module_name (str): 蓝图的名称，通常与模块名相同。
        import_name (str): 蓝图的导入名称 (通常是 __name__）。
        service_instance: 依赖的Service实例 (必须是BaseService或其子类)。
        config (dict): 包含模块特定配置的字典。预期结构：
            {
                'page_title': str, # 页面标题
                'header_title': str, # 页面头部标题
                'template_name': str, # 使用的Jinja2模板路径
                'columns_config': list[dict], # DataTables列配置 (用于排序映射等)
                    # e.g., [{'data': 'id', 'orderable': True}, {'data': 'model_name', 'orderable': True}, ...]
                    # 顺序必须与前端DataTables初始化时的列顺序严格对应（不含 select 和 actions 列）
                'searchable_fields': list[str], # 可用于简单搜索的字段名列表
                'filter_fields_config': list[dict], # 高级筛选器可用的字段配置 (传递给前端)
                    # e.g., [{'id': 'model_name', 'name': '模型名称', 'type': 'string'}, ...]
                'modal_form_fields': list[dict], # 添加/编辑模态框的表单字段配置 (传递给前端)
                    # e.g., [{'id': 'modelName', 'label': '模型名称', 'type': 'text', 'required': True}, ...]
                'default_sort': tuple, # 默认排序列和方向 e.g., ('sort_order', 'asc')
                'enable_batch_operations': dict, # 控制启用哪些批量操作, value为True表示启用
                    # e.g., {'delete': True, 'top': True, 'bottom': False, 'sync': True, 'unsync': True}
                # --- 以下是可选的 --- 
                # 'enable_actions_column': bool, # (前端JS用) 是否显示操作列 (默认True)
                # 'row_actions': list[dict] # (前端JS用) 定义额外的行内操作按钮配置
                    # e.g., [{'name': '详情', 'class': 'btn-info', 'icon': 'fa-eye', 'js_handler': 'showDetails'}]
                # 'global_actions': list[dict] # (前端JS用) 定义全局操作按钮（除标准CRUD外）
                    # e.g., [{'id': 'btnImport', 'text': '导入', 'class': 'btn-success', 'icon': 'fa-upload'}]
            }
    Returns:
        Blueprint: 配置好的Flask Blueprint实例。
    """
    # 验证 service_instance 类型
    if not isinstance(service_instance, BaseService):
        raise TypeError(f"service_instance must be an instance of BaseService or its subclass, got {type(service_instance).__name__}")

    # 验证基本配置项
    required_keys = ['page_title', 'header_title', 'template_name', 'columns_config',
                     'searchable_fields', 'filter_fields_config', 'modal_form_fields',
                     'default_sort', 'enable_batch_operations']
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required key in config: '{key}'")

    bp = Blueprint(module_name, import_name)
    current_app.logger.info(f"Creating blueprint '{module_name}'...")

    # --- 路由实现 --- 

    @bp.route('/', methods=['GET'])
    def index():
        """渲染数据表格主页面"""
        try:
            current_app.logger.debug(f"Rendering index for blueprint: {module_name}")
            # 准备传递给前端的配置
            frontend_config = config.copy()
            frontend_config['module_name'] = module_name
            frontend_config['api_base_url'] = f'/{module_name}'
            # 确保关键配置是JSON可序列化的 (通常字典/列表/基本类型没问题)

            return render_template(config['template_name'], config=frontend_config)
        except Exception as e:
            current_app.logger.error(f"Error rendering index for {module_name}: {e}", exc_info=True)
            # Consider rendering a generic error template
            return "Internal Server Error while rendering page.", 500

    @bp.route('/data', methods=['GET'])
    def get_data():
        """处理DataTables服务器端数据请求"""
        try:
            # 解析DataTables参数
            draw = request.args.get('draw', 1, type=int)
            start = request.args.get('start', 0, type=int)
            length = request.args.get('length', current_app.config.get('ITEMS_PER_PAGE', 20), type=int)
            search_value = request.args.get('search[value]', '')
            order_column_index_str = request.args.get('order[0][column]', None)
            order_dir = request.args.get('order[0][dir]', 'asc')
            advanced_filter_str = request.args.get('advanced_filter')
            current_app.logger.debug(f"Data request for {module_name}: draw={draw}, start={start}, length={length}, search={search_value}, order_col={order_column_index_str}, order_dir={order_dir}, filter={advanced_filter_str is not None}")

            # 计算分页
            page = (start // length) + 1 if length > 0 else 1
            per_page = length

            # 获取排序字段 (需要仔细映射前端索引到后端字段名)
            sort_by = config.get('default_sort', ('id', 'asc'))[0]
            order_column_config = config.get('columns_config', [])
            if order_column_index_str is not None:
                 try:
                     col_index = int(order_column_index_str)
                     # IMPORTANT: Adjust index based on whether frontend includes non-data columns (like checkbox) in the index count.
                     # Assuming frontend sends index RELATIVE to the columns defined in 'columns_config'
                     # If frontend includes checkbox (index 0), subtract 1: col_config_index = col_index - 1
                     # Let's assume frontend sends index relative to DATA columns configured here.
                     col_config_index = col_index

                     if 0 <= col_config_index < len(order_column_config):
                         column_conf = order_column_config[col_config_index]
                         col_data_field = column_conf.get('data') # Get the data field name
                         if col_data_field and column_conf.get('orderable', True):
                             sort_by = col_data_field
                             current_app.logger.debug(f"Sorting by mapped field: {sort_by}")
                         else:
                             current_app.logger.warning(f"{module_name}: Column index {col_index} maps to non-orderable or non-data column.")
                     else:
                          current_app.logger.warning(f"{module_name}: Invalid order column index received: {col_index}")
                 except (ValueError, IndexError) as e:
                     current_app.logger.warning(f"{module_name}: Error parsing order column index '{order_column_index_str}': {e}")
            else:
                # Use default if no order parameter sent
                sort_by = config.get('default_sort', ('id', 'asc'))[0]
                order_dir = config.get('default_sort', ('id', 'asc'))[1]
                current_app.logger.debug(f"Using default sort: {sort_by} {order_dir}")

            # 解析高级筛选
            filter_data = None
            if advanced_filter_str:
                try:
                    filter_data = json.loads(advanced_filter_str)
                    current_app.logger.info(f"Applying advanced filter for {module_name}: {json.dumps(filter_data, indent=2, ensure_ascii=False)}")
                except json.JSONDecodeError as e:
                    current_app.logger.error(f"Failed to decode advanced_filter JSON for {module_name}: {e}")
                    filter_data = None # Ignore filter on error

            # 调用Service获取数据
            result = service_instance.get_all(
                page=page,
                per_page=per_page,
                sort_by=sort_by,
                order=order_dir,
                search_value=search_value,
                searchable_fields=config.get('searchable_fields', []), # Get from config
                advanced_filter=filter_data
            )

            # 返回DataTables格式的JSON
            response_data = {
                'draw': draw,
                'recordsTotal': result['total'],
                'recordsFiltered': result['total'], # BaseService.get_all should return filtered count if implemented
                                                     # Assuming BaseDAO returns total count matching filter
                'data': result['data']
            }
            current_app.logger.debug(f"Data response for {module_name} prepared. Total: {result['total']}, Filtered: {result['total']}, Sent: {len(result['data'])}")
            return jsonify(response_data)

        except Exception as e:
            current_app.logger.error(f"Error fetching data for {module_name}: {e}", exc_info=True)
            return jsonify({
                'draw': request.args.get('draw', 1, type=int),
                'recordsTotal': 0,
                'recordsFiltered': 0,
                'data': [],
                'error': "处理请求时发生服务器内部错误。"
            }), 500

    @bp.route('/create', methods=['POST'])
    def create():
        """创建新记录"""
        try:
            data = request.json
            if not data:
                return jsonify({'success': False, 'message': '请求数据不能为空'}), 400
            current_app.logger.info(f"Create request for {module_name} with data: {data}")

            # Add validation based on config['modal_form_fields'] here if needed
            # ...

            model = service_instance.create(data)
            if model:
                return jsonify({'success': True, 'model': model.to_dict()}), 201
            else:
                # Check if DAO logged IntegrityError? Service might need to return more info.
                return jsonify({'success': False, 'message': '创建失败，数据可能无效或已存在。'}), 400
        except Exception as e:
            current_app.logger.error(f"Error creating record for {module_name}: {e}", exc_info=True)
            return jsonify({'success': False, 'message': '创建记录时发生服务器内部错误。'}), 500

    @bp.route('/update/<int:id>', methods=['PUT'])
    def update(id):
        """更新指定ID的记录"""
        try:
            data = request.json
            if not data:
                return jsonify({'success': False, 'message': '请求数据不能为空'}), 400
            current_app.logger.info(f"Update request for {module_name} id {id} with data: {data}")

            # Add validation based on config['modal_form_fields'] here if needed
            # ...

            model = service_instance.update(id, data)
            if model:
                return jsonify({'success': True, 'model': model.to_dict()})
            else:
                return jsonify({'success': False, 'message': '更新失败，记录未找到。'}), 404
        except Exception as e:
            current_app.logger.error(f"Error updating record id {id} for {module_name}: {e}", exc_info=True)
            return jsonify({'success': False, 'message': '更新记录时发生服务器内部错误。'}), 500

    @bp.route('/delete/<int:id>', methods=['DELETE'])
    def delete(id):
        """删除指定ID的记录（逻辑删除）"""
        try:
            current_app.logger.info(f"Delete request for {module_name} id {id}")
            success = service_instance.delete(id)
            if success:
                return jsonify({'success': True})
            else:
                # Could be not found or model doesn't support delete
                return jsonify({'success': False, 'message': '删除失败，记录未找到或无法删除。'}), 404
        except Exception as e:
            current_app.logger.error(f"Error deleting record id {id} for {module_name}: {e}", exc_info=True)
            return jsonify({'success': False, 'message': '删除记录时发生服务器内部错误。'}), 500

    @bp.route('/batch', methods=['POST'])
    def batch_action():
        """执行批量操作"""
        try:
            data = request.json
            if not data or not isinstance(data.get('ids'), list) or 'action' not in data:
                return jsonify({'success': False, 'message': '无效请求：需要ids列表和action。'}), 400

            ids = data.get('ids', [])
            action = data.get('action')
            enabled_actions = config.get('enable_batch_operations', {})
            current_app.logger.info(f"Batch action request for {module_name}: action='{action}', ids={ids}")

            if not ids:
                return jsonify({'success': False, 'message': '未选择任何记录进行操作。'}), 400

            # 检查操作是否被允许
            if not enabled_actions.get(action):
                current_app.logger.warning(f"Disallowed batch action '{action}' attempted for {module_name}.")
                return jsonify({'success': False, 'message': f'当前模块不支持批量操作: {action}'}), 400

            # 映射并执行Service方法
            count = 0
            service_method = None
            kwargs = {}
            action_map = {
                'delete': ('bulk_delete', {}),
                'top': ('batch_update_sort_order', {'action': 'top'}),
                'bottom': ('batch_update_sort_order', {'action': 'bottom'}),
                'sync': ('bulk_update_sync_status', {'need_sync': 1}),
                'unsync': ('bulk_update_sync_status', {'need_sync': 0}),
            }

            if action in action_map:
                 method_name, kwargs = action_map[action]
                 service_method = getattr(service_instance, method_name, None)
                 if service_method and callable(service_method):
                     count = service_method(ids=ids, **kwargs)
                     current_app.logger.info(f"Batch action '{action}' completed for {module_name}, affected: {count}")
                     return jsonify({'success': True, 'count': count, 'message': f'操作成功，影响了 {count} 条记录。'})
                 else:
                     # This indicates a potential mismatch between config and BaseService/DAO implementation
                     current_app.logger.error(f"Action '{action}' is enabled in config, but method '{method_name}' not found or not callable on service for {module_name}.")
                     return jsonify({'success': False, 'message': '服务器配置错误，无法执行该批量操作。'}), 500
            else:
                 # Should have been caught by enabled_actions check, but as a safeguard
                 current_app.logger.error(f"Unknown batch action '{action}' requested for {module_name}.")
                 return jsonify({'success': False, 'message': f'未知的批量操作: {action}'}), 400

        except Exception as e:
            current_app.logger.error(f"Error performing batch action '{data.get('action', 'unknown')}' for {module_name}: {e}", exc_info=True)
            # Use traceback.format_exc() for detailed error in logs if needed
            return jsonify({'success': False, 'message': '执行批量操作时发生服务器内部错误。'}), 500

    current_app.logger.info(f"Blueprint '{module_name}' created successfully.")
    return bp 