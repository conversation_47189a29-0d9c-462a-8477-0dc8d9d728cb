import os
import sys
from dotenv import load_dotenv

# 确保当前目录在Python路径中
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 加载环境变量
load_dotenv()

from app import create_app, db
from app.models.llm_ai_operate import LlmAiOperate

def init_app(app):
    """配置初始化应用的钩子函数
    
    这个函数用于在应用初始化时执行额外操作。
    目前实现为空方法，但可以根据需要添加特定的初始化逻辑。
    """
    pass

def get_config():
    """从环境变量构建配置字典
    
    Returns:
        dict: 包含所有配置项的字典
    
    Raises:
        ValueError: 当缺少必要的环境变量配置时
    """
    # 获取当前环境
    flask_env = os.getenv('FLASK_ENV', 'development')
    if flask_env not in ['development', 'production']:
        print(f"警告: 未知的环境名称 '{flask_env}'，将使用开发环境配置")
        flask_env = 'development'
        
    env_prefix = {'development': 'DEV_', 'production': 'PROD_'}.get(flask_env, 'DEV_')
    
    # 基础配置
    config = {
        'SECRET_KEY': os.getenv('SECRET_KEY', 'hard-to-guess-string'),
        'SQLALCHEMY_TRACK_MODIFICATIONS': os.getenv('SQLALCHEMY_TRACK_MODIFICATIONS', 'False').lower() == 'true',
        'SQLALCHEMY_RECORD_QUERIES': os.getenv('SQLALCHEMY_RECORD_QUERIES', 'True').lower() == 'true',
        'ITEMS_PER_PAGE': int(os.getenv('ITEMS_PER_PAGE', 100)),
        'API_REQUEST_TIMEOUT': int(os.getenv('API_REQUEST_TIMEOUT', 90)),
    }
    
    # 环境特定配置
    config['DEBUG'] = os.getenv(f'{env_prefix}DEBUG', 'True' if flask_env == 'development' else 'False').lower() == 'true'
    
    # 数据库URL
    db_user = os.getenv('DB_USER', '')
    db_password = os.getenv('DB_PASSWORD', '')
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '3306')
    db_name = os.getenv('DB_NAME', '')
    
    # 验证必要的数据库配置
    if not db_user:
        raise ValueError("缺少必要的环境变量配置: DB_USER")
    if not db_password:
        raise ValueError("缺少必要的环境变量配置: DB_PASSWORD")
    if not db_name:
        raise ValueError("缺少必要的环境变量配置: DB_NAME")
    
    # 根据环境构建数据库URL
    db_url = os.getenv(f'{env_prefix}DATABASE_URL')
    if not db_url:
        if flask_env == 'development':
            db_url = f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}_dev"
        else:  # production
            db_url = f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    config['SQLALCHEMY_DATABASE_URI'] = db_url
    
    # 添加init_app方法到配置字典中
    config['init_app'] = init_app
    
    return config

# 创建应用实例
try:
    config = get_config()
    app = create_app(config)
except Exception as e:
    print(f"错误: 创建应用时发生错误: {e}")
    raise

@app.shell_context_processor
def make_shell_context():
    return dict(db=db, LlmAiOperate=LlmAiOperate)

@app.cli.command('init_db')
def init_db():
    """初始化数据库"""
    db.create_all()
    print('数据库初始化完成')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.getenv('PORT', 5000)))