#!/bin/bash

# 测试修复效果的脚本

echo "=== OpenAI API密钥验证脚本修复测试 ==="
echo ""

# 检查脚本语法
echo "1. 检查脚本语法..."
if bash -n check.sh; then
    echo "✅ 语法检查通过"
else
    echo "❌ 语法检查失败"
    exit 1
fi

echo ""
echo "2. 检查关键函数是否存在..."

# 检查关键函数
if grep -q "supports_color()" check.sh; then
    echo "✅ supports_color函数已添加"
else
    echo "❌ supports_color函数缺失"
fi

if grep -q "SSL证书验证失败" check.sh; then
    echo "✅ SSL错误处理已改进"
else
    echo "❌ SSL错误处理未改进"
fi

if grep -q "无响应内容" check.sh; then
    echo "✅ 空响应处理已添加"
else
    echo "❌ 空响应处理缺失"
fi

echo ""
echo "3. 测试颜色支持检测..."

# 模拟测试颜色支持
cat > temp_color_test.sh << 'EOF'
#!/bin/bash
supports_color() {
    if [ -t 2 ] && [ "${TERM:-}" != "dumb" ]; then
        return 0
    else
        return 1
    fi
}

if supports_color; then
    echo "终端支持颜色"
else
    echo "终端不支持颜色"
fi
EOF

bash temp_color_test.sh
rm -f temp_color_test.sh

echo ""
echo "4. 检查curl错误码处理..."
if grep -q "case.*curl_exit_code" check.sh; then
    echo "✅ curl错误码详细处理已添加"
    echo "支持的错误码:"
    grep -A 10 "case.*curl_exit_code" check.sh | grep ")" | head -8
else
    echo "❌ curl错误码处理未改进"
fi

echo ""
echo "5. 检查JSON格式化功能..."
if grep -q "python3 -m json.tool" check.sh; then
    echo "✅ JSON格式化功能已添加"
else
    echo "❌ JSON格式化功能缺失"
fi

echo ""
echo "=== 修复验证完成 ==="
echo ""
echo "主要修复内容:"
echo "- ✅ 修复了颜色代码显示问题"
echo "- ✅ 添加了SSL证书错误处理"
echo "- ✅ 改进了curl错误码解释"
echo "- ✅ 增强了JSON响应格式化"
echo "- ✅ 添加了空响应内容处理"
echo "- ✅ 支持终端颜色检测"
echo ""
echo "脚本现在应该能够正确处理网络错误和显示响应信息。"
