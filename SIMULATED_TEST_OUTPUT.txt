# 模拟的脚本运行输出示例

$ bash check.sh test_keys.txt

[信息] OpenAI API密钥批量验证工具
[信息] 开始时间: 2024-12-19 15:30:45

[信息] 配置参数:
[信息]   API URL: https://api.openai.com/v1/models
[信息]   前缀要求: sk
[信息]   长度要求: 不检查
[信息]   失败关键词: suspended
[信息]   日志文件: api_test_20241219_153045.log

[信息] 加载密钥中...
[信息] 从文件读取: test_keys.txt
[警告] 移除了 1 个重复密钥
[成功] 共加载 6 个唯一密钥

[信息] 开始格式验证...
[成功] 格式验证完成: 6 个密钥中 2 个通过

[信息] 开始API验证 2 个密钥...
========================================
[  1/  2] 验证密钥 sk-svcacct--6QW*** ... ✓ 有效
    → 可访问 15 个模型
[  2/  2] 验证密钥 sk-123456789012*** ... ✗ 无效 [HTTP 401]
    → 错误: Invalid API key provided
========================================
[成功] API验证完成: 2 个密钥中 1 个有效

========================================
[信息] 测试结果汇总:
========================================

[信息] 总共测试密钥: 2
[成功] 有效密钥数量: 1
[错误] 无效密钥数量: 1

[信息] 详细测试结果:
----------------------------------------
✓ sk-svcacct--6QW*** [HTTP 200] API验证成功, 可访问模型数: 15
✗ sk-123456789012*** [HTTP 401] HTTP错误码: 401, 错误信息: Invalid API key provided
========================================

[成功] 找到 1 个有效密钥:
----------------------------------------
***********************************************************************************************************************************************************************

[信息] 有效密钥已保存到: valid_keys_20241219_153045.txt
[信息] 详细日志已保存到: api_test_20241219_153045.log

# 生成的日志文件内容示例 (api_test_20241219_153045.log):

# OpenAI API密钥验证测试日志
[2024-12-19 15:30:45] [START] 开始执行密钥验证测试
[2024-12-19 15:30:45] [CONFIG] API_URL=https://api.openai.com/v1/models, KEY_PREFIX=sk, KEY_LENGTH=0, FAIL_KEYWORD=suspended
[2024-12-19 15:30:46] [API_TEST] 密钥: sk-svcacct--6QW*** | HTTP状态: 200 | 结果: VALID
[2024-12-19 15:30:46] [API_RESPONSE] 响应内容: {"object":"list","data":[{"id":"gpt-4","object":"model","created":1687882411,"owned_by":"openai"},{"id":"gpt-3.5-turbo","object":"model","created":1677610602,"owned_by":"openai"},...]}
[2024-12-19 15:30:46] [DETAIL] API验证成功, 可访问模型数: 15
[2024-12-19 15:30:47] [API_TEST] 密钥: sk-123456789012*** | HTTP状态: 401 | 结果: HTTP_ERROR
[2024-12-19 15:30:47] [API_RESPONSE] 响应内容: {"error":{"message":"Invalid API key provided","type":"invalid_request_error","param":null,"code":"invalid_api_key"}}
[2024-12-19 15:30:47] [DETAIL] HTTP错误码: 401, 错误信息: Invalid API key provided
[2024-12-19 15:30:47] [RESULT] 测试完成，有效密钥: 1/2
[2024-12-19 15:30:47] [END] 密钥验证测试完成

# 生成的有效密钥文件内容 (valid_keys_20241219_153045.txt):
***********************************************************************************************************************************************************************
