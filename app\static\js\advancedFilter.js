/**
 * AdvancedFilter - 通用高级筛选模块
 * 为数据表格提供灵活的高级筛选功能
 * @version 1.0.0
 */
class AdvancedFilter {
    /**
     * 创建高级筛选实例
     * @param {Object} options - 配置选项
     */
    constructor(options) {
        // 默认选项和传入选项合并
        this.options = Object.assign({
            // 必须由调用者提供的字段定义
            filterFields: [],
            
            // 存储相关配置
            storagePrefix: 'filter_',
            storageKey: null, // 如果未提供，将使用 storagePrefix + 'advanced_filter'
            
            // 操作符配置（可选，有默认值）
            filterOperators: null, // 如果未提供，将使用默认操作符配置
            
            // UI元素选择器
            containerSelectors: {
                topLevelGroups: '#topLevelGroups',
                btnAddTopLevelGroup: '#btnAddTopLevelGroup',
                btnApplyFilter: '#btnApplyAdvancedFilter',
                btnResetFilter: '#btnResetAdvancedFilter',
                btnTestFilter: '#btnTestFilter'
            },
            
            // DataTable实例，用于应用筛选
            dataTable: null,
            
            // 回调函数
            onFilterApplied: null, // 筛选应用后的回调
            onFilterReset: null    // 筛选重置后的回调
        }, options);
        
        // 如果未提供storageKey，根据前缀生成
        if (!this.options.storageKey) {
            this.options.storageKey = this.options.storagePrefix + 'advanced_filter';
        }
        
        // 如果未提供操作符配置，使用默认配置
        if (!this.options.filterOperators) {
            this.options.filterOperators = this._getDefaultOperators();
        }
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化高级筛选模块
     */
    init() {
        // 绑定UI事件
        this._bindEvents();
        
        // 加载保存的筛选条件
        this._loadSavedFilter();
    }
    
    /**
     * 获取筛选条件JSON
     * @returns {Object} 筛选条件JSON对象
     */
    getFilterJson() {
        const groups = [];
        let topLevelOperator = 'AND';
        const topLevelContainer = $(this.options.containerSelectors.topLevelGroups);
        
        // 先查找第一个条件关系符，确定顶层操作符
        const firstRelation = topLevelContainer.children('.condition-relation').first();
        if (firstRelation.length > 0) {
            topLevelOperator = firstRelation.find('.relation-or').hasClass('active') ? 'OR' : 'AND';
        }
        
        // 获取所有顶层组
        const topGroups = topLevelContainer.children('.filter-group');
        
        topGroups.each(function(index) {
            const groupElement = $(this);
            const groupData = getGroupJson(groupElement);
            
            if (groupData.conditions && groupData.conditions.length > 0) {
                // 对于除最后一个组之外的所有组，找到其后的关系符
                if (index < topGroups.length - 1) {
                    const nextRelation = groupElement.next('.condition-relation');
                    if (nextRelation.length) {
                        const isOr = nextRelation.find('.relation-or').hasClass('active');
                        groupData.relationOperator = isOr ? 'OR' : 'AND';
                    }
                }
                groups.push(groupData);
            }
        });
        
        return { operator: topLevelOperator, groups: groups };
        
        // 递归获取组数据
        function getGroupJson(group) {
            const conditions = [];
            let groupOperator = 'AND';
            let firstRelFound = false;
            let lastItemData = null;
            
            group.find('> .filter-conditions').children().each(function() {
                const element = $(this);
                if (element.hasClass('condition-relation')) {
                    const op = element.find('.relation-or').hasClass('active') ? 'OR' : 'AND';
                    if (!firstRelFound) {
                        groupOperator = op;
                        firstRelFound = true;
                    }
                    if (lastItemData) {
                        lastItemData.relationOperator = op;
                    }
                } else if (element.hasClass('filter-condition')) {
                    const fs = element.find('.filter-field');
                    const os = element.find('.filter-operator');
                    const vi = element.find('.filter-value');
                    const f = fs.val();
                    const o = os.val();
                    const ft = fs.find('option:selected').data('type');
                    
                    if (f && o) {
                        const cd = {
                            type: 'condition',
                            field: f,
                            field_type: ft,
                            operator: o
                        };
                        
                        if (o !== 'is_null' && o !== 'is_not_null') {
                            let rv = vi.val();
                            let v = rv;
                            if (rv !== null && rv !== undefined && rv !== '') {
                                if (ft === 'number') {
                                    v = Number(rv);
                                } else if (ft === 'boolean') {
                                    v = (rv === 'true' || rv === '1');
                                } else if (ft === 'date') {
                                    v = rv;
                                }
                            } else if (ft !== 'string') {
                                v = null;
                            }
                            cd.value = v;
                        }
                        
                        conditions.push(cd);
                        lastItemData = cd;
                    }
                } else if (element.hasClass('filter-group')) {
                    const ngd = getGroupJson(element);
                    if (ngd.conditions && ngd.conditions.length > 0) {
                        ngd.type = 'group';
                        conditions.push(ngd);
                        lastItemData = ngd;
                    }
                }
            });
            
            return { operator: groupOperator, conditions: conditions };
        }
    }
    
    /**
     * 根据JSON设置筛选条件UI
     * @param {Object} json - 筛选条件JSON对象
     */
    setFilterFromJson(json) {
        const self = this;
        const container = $(this.options.containerSelectors.topLevelGroups);
        
        // 确保完全清空容器
        container.empty();
        
        if (!json || !json.groups || !json.groups.length) {
            return;
        }
        
        // 存储顶层操作符，默认为AND
        const topLevelOperator = json.operator || 'AND';
        
        json.groups.forEach((groupJson, index) => {
            // 获取当前容器状态，避免使用可能过时的引用
            const currentContainer = $(self.options.containerSelectors.topLevelGroups);
            
            // 只有当不是第一个条件组时，才添加关系分隔符
            if (index > 0) {
                // 使用上一个条件组的relationOperator，如果没有则使用顶层操作符
                const relOp = json.groups[index - 1].relationOperator || topLevelOperator;
                
                // 获取容器的最后一个元素
                const lastElement = currentContainer.children().last();
                
                // 确保最后一个元素是条件组而不是关系分隔符
                if (lastElement.hasClass('filter-group')) {
                    self._addRelationSeparator(currentContainer, relOp);
                } else if (lastElement.hasClass('condition-relation')) {
                    // 如果最后一个元素已经是关系分隔符，更新其状态而不是添加新的
                    const relationBtns = lastElement.find('.btn-group button');
                    relationBtns.removeClass('active');
                    if (relOp === 'OR') {
                        lastElement.find('.relation-or').addClass('active');
                    } else {
                        lastElement.find('.relation-and').addClass('active');
                    }
                }
            }
            
            // 添加新的条件组
            const newGroup = self._addFilterGroup(currentContainer, groupJson.operator);
            
            // 设置条件组的内容
            self._setGroupConditionsFromJson(newGroup.find('> .filter-conditions'), groupJson);
        });
        
        // 最终检查，确保没有多余的关系分隔符
        const finalContainer = $(self.options.containerSelectors.topLevelGroups);
        const children = finalContainer.children();
        
        // 检查第一个元素是否为关系分隔符，如果是则移除（应该从条件组开始）
        if (children.first().hasClass('condition-relation')) {
            children.first().remove();
        }
        
        // 检查最后一个元素是否为关系分隔符，如果是则移除（最后应该是条件组）
        if (children.last().hasClass('condition-relation')) {
            children.last().remove();
        }
        
        // 检查是否有连续的关系分隔符，移除多余的
        children.each(function(index) {
            if (index > 0) {
                const $this = $(this);
                const $prev = $(children[index - 1]);
                if ($this.hasClass('condition-relation') && $prev.hasClass('condition-relation')) {
                    $this.remove();
                }
            }
        });
    }
    
    /**
     * 设置组条件
     * @param {jQuery} container - 容器元素
     * @param {Object} groupJson - 组JSON
     * @private
     */
    _setGroupConditionsFromJson(container, groupJson) {
        const self = this;
        
        container.empty();
        
        if (!groupJson || !groupJson.conditions || !groupJson.conditions.length) {
            return;
        }
        
        groupJson.conditions.forEach((itemJson, index) => {
            if (index > 0) {
                const relOp = groupJson.conditions[index - 1].relationOperator || groupJson.operator || 'AND';
                self._addRelationSeparator(container, relOp);
            }
            
            if (itemJson.type === 'condition') {
                self._addFilterCondition(container, itemJson.field, itemJson.operator, itemJson.value);
            } else if (itemJson.type === 'group') {
                const nestedGroup = self._addFilterGroup(container, itemJson.operator);
                self._setGroupConditionsFromJson(nestedGroup.find('> .filter-conditions'), itemJson);
            }
        });
    }
    
    /**
     * 应用筛选条件
     */
    applyFilter() {
        // 获取筛选JSON
        const filterJson = this.getFilterJson();
        
        // 检查是否有有效条件
        let hasValidConditions = this._hasValidConditions(filterJson);
        
        // 如果有有效条件，保存并应用
        if (hasValidConditions) {
            // 保存到localStorage
            localStorage.setItem(this.options.storageKey, JSON.stringify(filterJson));
            
            // 如果有DataTable实例，重新加载数据
            if (this.options.dataTable) {
                this.options.dataTable.ajax.reload();
            }
            
            // 调用回调（如果有）
            if (typeof this.options.onFilterApplied === 'function') {
                this.options.onFilterApplied(filterJson);
            }
        }
    }
    
    /**
     * 重置筛选条件
     */
    resetFilter() {
        // 获取容器元素
        const container = $(this.options.containerSelectors.topLevelGroups);
        
        // 彻底清空容器，包括所有子元素和绑定的事件
        container.empty();
        
        // 额外检查：确保没有任何残留元素
        if (container.children().length > 0) {
            // 如果empty()方法没有完全清空，使用更直接的方式
            container.html('');
        }
        
        // 清除localStorage
        localStorage.removeItem(this.options.storageKey);
        
        // 如果有DataTable实例，重新加载数据
        if (this.options.dataTable) {
            this.options.dataTable.ajax.reload();
        }
        
        // 调用回调（如果有）
        if (typeof this.options.onFilterReset === 'function') {
            this.options.onFilterReset();
        }
    }
    
    /**
     * 添加顶层筛选组
     * @returns {jQuery} 新添加的筛选组元素
     */
    addTopLevelGroup() {
        const container = $(this.options.containerSelectors.topLevelGroups);
        
        // 检查是否已经有条件组
        const hasExistingGroups = container.children('.filter-group').length > 0;
        
        if (hasExistingGroups) {
            // 1. 先移除所有多余的关系分隔符（相邻的关系分隔符）
            const relations = container.children('.condition-relation');
            relations.each(function(index) {
                const $this = $(this);
                const nextIsRelation = $this.next('.condition-relation').length > 0;
                if (nextIsRelation) {
                    $this.remove();
                }
            });
            
            // 2. 确保关系分隔符不是容器的最后一个元素
            const lastElement = container.children().last();
            if (lastElement.hasClass('condition-relation')) {
                lastElement.remove();
            }
            
            // 3. 确保每个条件组后面都有且只有一个关系分隔符
            const lastGroup = container.children('.filter-group').last();
            const existingRelation = lastGroup.next('.condition-relation');
            if (!existingRelation.length) {
                this._addRelationSeparator(container);
            }
        }
        
        return this._addFilterGroup(container);
    }
    
    /**
     * 为DataTable配置高级筛选支持
     * @param {Object} dtConfig - DataTable配置对象
     * @returns {Object} 更新后的DataTable配置对象
     */
    setupDataTableFilter(dtConfig) {
        const self = this;
        
        // 确保ajax存在
        dtConfig.ajax = dtConfig.ajax || {};
        
        // 如果ajax是字符串（URL），转换为对象
        if (typeof dtConfig.ajax === 'string') {
            const url = dtConfig.ajax;
            dtConfig.ajax = { url: url };
        }
        
        // 保存原始的data函数（如果有）
        const originalDataFn = dtConfig.ajax.data;
        
        // 添加高级筛选数据到请求
        dtConfig.ajax.data = function(d) {
            // 调用原始data函数（如果有）
            if (typeof originalDataFn === 'function') {
                originalDataFn(d);
            }
            
            // 获取筛选条件并添加到请求
            const filterJson = self.getFilterJson();
            if (self._hasValidConditions(filterJson)) {
                console.log('Applying filter:', JSON.stringify(filterJson));
                d.advanced_filter = JSON.stringify(filterJson);
            }
            
            return d;
        };
        
        return dtConfig;
    }
    
    // 以下是私有方法（按约定以下划线开头）
    
    /**
     * 获取默认操作符配置
     * @returns {Object} 默认操作符配置
     * @private
     */
    _getDefaultOperators() {
        return {
            string: [
                { id: 'eq', name: '等于' }, { id: 'neq', name: '不等于' },
                { id: 'contains', name: '包含' }, { id: 'not_contains', name: '不包含' },
                { id: 'starts_with', name: '开头是' }, { id: 'ends_with', name: '结尾是' },
                { id: 'is_null', name: '是空' }, { id: 'is_not_null', name: '非空' }
            ],
            number: [
                { id: 'eq', name: '等于' }, { id: 'neq', name: '不等于' },
                { id: 'gt', name: '大于' }, { id: 'gte', name: '大于等于' },
                { id: 'lt', name: '小于' }, { id: 'lte', name: '小于等于' },
                { id: 'is_null', name: '是空' }, { id: 'is_not_null', name: '非空' }
            ],
            date: [
                { id: 'eq', name: '等于' }, { id: 'neq', name: '不等于' },
                { id: 'gt', name: '晚于' }, { id: 'gte', name: '晚于等于' },
                { id: 'lt', name: '早于' }, { id: 'lte', name: '早于等于' },
                { id: 'is_null', name: '是空' }, { id: 'is_not_null', name: '非空' }
            ],
            boolean: [
                { id: 'eq', name: '等于' }
            ]
        };
    }
    
    /**
     * 绑定UI事件
     * @private
     */
    _bindEvents() {
        const self = this;
        
        // 添加顶层筛选组按钮
        $(this.options.containerSelectors.btnAddTopLevelGroup).on('click', function() {
            self.addTopLevelGroup();
        });
        
        // 应用筛选按钮
        $(this.options.containerSelectors.btnApplyFilter).on('click', function() {
            self.applyFilter();
        });
        
        // 重置筛选按钮
        $(this.options.containerSelectors.btnResetFilter).on('click', function() {
            self.resetFilter();
        });
        
        // 测试筛选按钮（如果存在）
        if ($(this.options.containerSelectors.btnTestFilter).length) {
            $(this.options.containerSelectors.btnTestFilter).on('click', function() {
                const filterJson = self.getFilterJson();
                console.log('Filter JSON:', JSON.stringify(filterJson, null, 2));
                alert('筛选条件已输出到控制台 (F12)');
            });
        }
    }
    
    /**
     * 检查筛选条件是否有效
     * @param {Object} filterJson - 筛选条件JSON对象
     * @returns {boolean} 是否有有效条件
     * @private
     */
    _hasValidConditions(filterJson) {
        if (!filterJson || !filterJson.groups || !Array.isArray(filterJson.groups)) {
            return false;
        }
        
        for (const group of filterJson.groups) {
            if (group.conditions && Array.isArray(group.conditions) && group.conditions.length > 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 加载已保存的筛选条件
     * @private
     */
    _loadSavedFilter() {
        const savedFilter = localStorage.getItem(this.options.storageKey);
        if (savedFilter) {
            try {
                const filterJson = JSON.parse(savedFilter);
                this.setFilterFromJson(filterJson);
            } catch (e) {
                console.error('Failed to parse saved filter:', e);
                localStorage.removeItem(this.options.storageKey);
            }
        }
    }
    
    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     * @private
     */
    _generateId() {
        return 'filter_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
    }
    
    /**
     * 创建字段选择HTML
     * @returns {string} 字段选择HTML
     * @private
     */
    _createFieldSelectHtml() {
        let html = '<select class="form-control form-control-sm filter-field">';
        this.options.filterFields.forEach(field => {
            html += `<option value="${field.id}" data-type="${field.type}">${field.name}</option>`;
        });
        html += '</select>';
        return html;
    }
    
    /**
     * 创建操作符选择HTML
     * @param {string} fieldType - 字段类型
     * @returns {string} 操作符选择HTML
     * @private
     */
    _createOperatorSelectHtml(fieldType) {
        let operators = this.options.filterOperators[fieldType] || [];
        let html = '<select class="form-control form-control-sm filter-operator">';
        operators.forEach(op => {
            html += `<option value="${op.id}">${op.name}</option>`;
        });
        html += '</select>';
        return html;
    }
    
    /**
     * 创建值输入HTML
     * @param {string} fieldType - 字段类型
     * @param {string} operatorId - 操作符ID
     * @returns {string} 值输入HTML
     * @private
     */
    _createValueInputHtml(fieldType, operatorId) {
        const noValueOperators = ['is_null', 'is_not_null'];
        if (noValueOperators.includes(operatorId)) {
            return '';
        }
        
        let inputType = 'text';
        let selectOptions = '';
        
        switch (fieldType) {
            case 'number':
                inputType = 'number';
                break;
            case 'date':
                inputType = 'datetime-local';
                break;
            case 'boolean':
                inputType = 'select';
                selectOptions = '<option value="1">是 (已删除)</option><option value="0">否 (正常)</option>';
                break;
        }
        
        if (inputType === 'select') {
            return `<select class="form-control form-control-sm filter-value">${selectOptions}</select>`;
        }
        
        return `<input type="${inputType}" class="form-control form-control-sm filter-value">`;
    }
    
    /**
     * 绑定筛选条件事件
     * @param {jQuery} condition - 条件元素
     * @private
     */
    _bindFilterEvents(condition) {
        const self = this;
        
        condition.find('.filter-field').on('change', function() {
            const fieldType = $(this).find('option:selected').data('type');
            const opContainer = condition.find('.filter-operator-container');
            const valContainer = condition.find('.filter-value-container');
            
            opContainer.html(self._createOperatorSelectHtml(fieldType));
            valContainer.html(self._createValueInputHtml(fieldType, opContainer.find('.filter-operator').val()));
        });
        
        condition.find('.filter-operator-container').on('change', '.filter-operator', function() {
            const fieldType = condition.find('.filter-field option:selected').data('type');
            condition.find('.filter-value-container').html(self._createValueInputHtml(fieldType, $(this).val()));
        });
        
        condition.find('.btn-remove-condition').on('click', function() {
            const relBefore = condition.prev('.condition-relation');
            const relAfter = condition.next('.condition-relation');
            
            if (relAfter.length) {
                relAfter.remove();
            } else if (relBefore.length) {
                relBefore.remove();
            }
            
            condition.remove();
        });
    }
    
    /**
     * 添加关系分隔符
     * @param {jQuery} container - 容器元素
     * @param {string} defaultOperator - 默认操作符
     * @returns {jQuery} 关系分隔符元素
     * @private
     */
    _addRelationSeparator(container, defaultOperator = 'AND') {
        const relationId = this._generateId();
        
        const relationHtml = `
            <div class="condition-relation d-flex align-items-center py-2" data-relation-id="${relationId}">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary ${defaultOperator === 'AND' ? 'active' : ''} relation-and">AND</button>
                    <button type="button" class="btn btn-outline-primary ${defaultOperator === 'OR' ? 'active' : ''} relation-or">OR</button>
                </div>
            </div>
        `;
        
        container.append(relationHtml);
        
        const relation = container.find(`.condition-relation[data-relation-id="${relationId}"]`);
        relation.find('.btn').on('click', function() {
            $(this).addClass('active').siblings().removeClass('active');
        });
        
        return relation;
    }
    
    /**
     * 添加筛选条件
     * @param {jQuery} container - 容器元素
     * @param {string} field - 字段ID
     * @param {string} operator - 操作符ID
     * @param {*} value - 条件值
     * @returns {jQuery} 条件元素
     * @private
     */
    _addFilterCondition(container, field = null, operator = null, value = null) {
        const self = this;
        const hasExistingItems = container.children().length > 0;
        
        if (hasExistingItems) {
            this._addRelationSeparator(container);
        }
        
        const condId = this._generateId();
        const initialField = this.options.filterFields.find(f => f.id === field) || this.options.filterFields[0];
        const initialType = initialField.type;
        const initialOp = operator || (this.options.filterOperators[initialType]?.[0]?.id || 'eq');
        
        let condHtml = `
            <div class="filter-condition p-2 border rounded mb-2" data-condition-id="${condId}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group mb-1">
                            <label class="small mb-1">字段</label>
                            ${this._createFieldSelectHtml()}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group mb-1">
                            <label class="small mb-1">操作符</label>
                            <div class="filter-operator-container">
                                ${this._createOperatorSelectHtml(initialType)}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="form-group mb-1">
                            <label class="small mb-1">值</label>
                            <div class="filter-value-container">
                                ${this._createValueInputHtml(initialType, initialOp)}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 d-flex align-items-end justify-content-end">
                        <button type="button" class="btn btn-sm btn-danger btn-remove-condition mb-1">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        container.append(condHtml);
        
        const newCondition = container.find(`.filter-condition[data-condition-id="${condId}"]`);
        
        if (field) newCondition.find('.filter-field').val(field);
        if (operator) newCondition.find('.filter-operator').val(operator);
        
        if (value !== null && value !== undefined) {
            const valueInput = newCondition.find('.filter-value');
            if (valueInput.is('select')) {
                valueInput.val(String(value === true || value === 'true' || value === 1 || value === '1' ? 1 : 0));
            } else {
                valueInput.val(value);
            }
        }
        
        this._bindFilterEvents(newCondition);
        
        return newCondition;
    }
    
    /**
     * 添加筛选组
     * @param {jQuery} container - 容器元素
     * @param {string} operator - 操作符
     * @returns {jQuery} 组元素
     * @private
     */
    _addFilterGroup(container, operator = 'AND') {
        const self = this;
        const hasExistingItems = container.children().length > 0;
        
        if (hasExistingItems && container.attr('id') !== this.options.containerSelectors.topLevelGroups.replace('#', '')) {
            this._addRelationSeparator(container);
        }
        
        const groupId = this._generateId();
        
        const groupHtml = `
            <div class="filter-group border rounded p-3 mb-2" data-group-id="${groupId}">
                <div class="filter-group-header d-flex justify-content-between align-items-center">
                    <span class="font-weight-bold">条件组</span>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary btn-add-group-condition">
                            <i class="fas fa-plus"></i> 添加条件
                        </button>
                        <button type="button" class="btn btn-outline-info btn-add-nested-group">
                            <i class="fas fa-layer-group"></i> 添加嵌套组
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-remove-group">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="filter-conditions"></div>
            </div>
        `;
        
        container.append(groupHtml);
        
        const newGroup = container.find(`.filter-group[data-group-id="${groupId}"]`);
        this._bindGroupEvents(newGroup);
        
        this._addFilterCondition(newGroup.find('.filter-conditions'));
        
        return newGroup;
    }
    
    /**
     * 绑定组事件
     * @param {jQuery} group - 组元素
     * @private
     */
    _bindGroupEvents(group) {
        const self = this;
        
        group.find('.btn-add-group-condition').first().on('click', function(e) {
            e.stopPropagation();
            self._addFilterCondition($(this).closest('.filter-group').find('> .filter-conditions'));
        });
        
        group.find('.btn-add-nested-group').first().on('click', function(e) {
            e.stopPropagation();
            self._addFilterGroup($(this).closest('.filter-group').find('> .filter-conditions'));
        });
        
        group.find('.btn-remove-group').first().on('click', function(e) {
            e.stopPropagation();
            const currentGroup = $(this).closest('.filter-group');
            const relBefore = currentGroup.prev('.condition-relation');
            const relAfter = currentGroup.next('.condition-relation');
            
            if (relAfter.length) {
                relAfter.remove();
            } else if (relBefore.length) {
                relBefore.remove();
            }
            
            currentGroup.remove();
        });
    }
}

// 如果在浏览器环境中，将模块暴露为全局变量
if (typeof window !== 'undefined') {
    window.AdvancedFilter = AdvancedFilter;
} 