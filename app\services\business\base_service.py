from flask import current_app
from flask_login import current_user
from app.dao.base_dao import BaseDAO

class BaseService:
    """通用业务逻辑服务基类"""

    def __init__(self, dao_instance):
        """
        初始化Service

        Args:
            dao_instance: 依赖的DAO实例 (必须是BaseDAO或其子类)
        """
        if not isinstance(dao_instance, BaseDAO):
            raise TypeError(f"dao_instance must be an instance of BaseDAO or its subclass, got {type(dao_instance).__name__}")
        self.dao = dao_instance
        current_app.logger.debug(f"BaseService initialized with DAO for model: {self.dao.model_class.__name__}")

    def get_all(self, page=1, per_page=20, **kwargs):
        """获取所有记录，支持分页、排序、搜索和筛选"""
        current_app.logger.debug(f"Service get_all called for {self.dao.model_class.__name__}: page={page}, per_page={per_page}, kwargs={kwargs}")

        # 处理"全部"选项，但设置一个最大限制防止性能问题
        if per_page == -1:
            max_per_page = current_app.config.get('MAX_ITEMS_PER_PAGE', 10000) # 从配置获取上限
            current_app.logger.info(f"Requested all items, setting per_page to max: {max_per_page}")
            per_page = max_per_page
        elif per_page <= 0:
             per_page = current_app.config.get('ITEMS_PER_PAGE', 20) # Use default if invalid

        # 调用DAO获取分页对象
        # searchable_fields 参数应由调用方 (通常是View层) 根据配置传入kwargs
        pagination = self.dao.get_paginated(
            page=page,
            per_page=per_page,
            **kwargs # 传递 sort_by, order, search_value, searchable_fields, advanced_filter 等
        )

        # 提取结果并转换为字典列表 (假设模型有to_dict方法)
        data_list = []
        for item in pagination.items:
             if hasattr(item, 'to_dict') and callable(item.to_dict):
                 data_list.append(item.to_dict())
             else:
                 current_app.logger.warning(f"Model {self.dao.model_class.__name__} instance (id: {getattr(item, 'id', 'N/A')}) lacks a callable to_dict() method.")
                 # Optionally, create a default dict representation here
                 data_list.append({'id': getattr(item, 'id', None)}) # Basic fallback

        result = {
            'data': data_list,
            'total': pagination.total,
            'current_page': pagination.page,
            'per_page': pagination.per_page, # Use the actual per_page from pagination
            'total_pages': pagination.pages,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }

        return result

    def _inject_user_context(self, data, is_create=False):
        """尝试注入创建人/更新人信息"""
        try:
            # 检查Flask-Login是否可用且用户已登录
            if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
                username = current_user.username
                if is_create:
                    if hasattr(self.dao.model_class, 'create_by') and 'create_by' not in data:
                        data['create_by'] = username
                        current_app.logger.debug(f"Injected create_by: {username}")
                    # Also set update_by on create if model supports it
                    if hasattr(self.dao.model_class, 'update_by') and 'update_by' not in data:
                         data['update_by'] = username
                         current_app.logger.debug(f"Injected update_by on create: {username}")
                else: # is update
                    if hasattr(self.dao.model_class, 'update_by') and 'update_by' not in data:
                        data['update_by'] = username
                        current_app.logger.debug(f"Injected update_by: {username}")
            else:
                 current_app.logger.debug("User not authenticated, skipping user context injection.")
        except AttributeError:
             current_app.logger.debug("current_user not available or Flask-Login not configured, skipping user context injection.")
        except Exception as e:
            current_app.logger.error(f"Error injecting user context: {e}", exc_info=True)
        return data

    def create(self, data):
        """创建新记录，注入创建人信息"""
        data = self._inject_user_context(data.copy(), is_create=True)
        return self.dao.create(data)

    def update(self, id, data):
        """更新记录，注入更新人信息"""
        data = self._inject_user_context(data.copy(), is_create=False)
        return self.dao.update(id, data)

    def delete(self, id):
        """删除记录(逻辑删除)"""
        # Note: DAO's delete method already handles update_by injection potentially
        return self.dao.delete(id)

    def bulk_delete(self, ids):
        """批量删除记录(逻辑删除)"""
         # Note: DAO's bulk_delete method already handles update_by injection potentially
        return self.dao.bulk_delete(ids)

    # --- Optional Method Wrappers --- 
    # These wrap DAO methods, mainly for consistency in the service layer
    # and potentially adding service-level checks or logic later.

    def get_by_id(self, id):
        """根据ID获取活跃记录"""
        # Usually, service layer retrieves active records
        return self.dao.get_active_by_id(id)

    def update_sort_order(self, id, action):
        """更新排序顺序 (如果DAO支持)"""
        if hasattr(self.dao, 'update_sort_order') and callable(self.dao.update_sort_order):
            return self.dao.update_sort_order(id, action)
        else:
            current_app.logger.warning(f"DAO for {self.dao.model_class.__name__} does not support update_sort_order.")
            return False

    def batch_update_sort_order(self, ids, action):
        """批量更新排序顺序 (如果DAO支持)"""
        if hasattr(self.dao, 'batch_update_sort_order') and callable(self.dao.batch_update_sort_order):
            return self.dao.batch_update_sort_order(ids, action)
        else:
            current_app.logger.warning(f"DAO for {self.dao.model_class.__name__} does not support batch_update_sort_order.")
            return 0

    def update_sync_status(self, id, need_sync=1):
        """更新同步状态 (如果DAO支持)"""
        if hasattr(self.dao, 'update_sync_status') and callable(self.dao.update_sync_status):
            return self.dao.update_sync_status(id, need_sync)
        else:
            current_app.logger.warning(f"DAO for {self.dao.model_class.__name__} does not support update_sync_status.")
            return False

    def bulk_update_sync_status(self, ids, need_sync=1):
        """批量更新同步状态 (如果DAO支持)"""
        if hasattr(self.dao, 'bulk_update_sync_status') and callable(self.dao.bulk_update_sync_status):
            return self.dao.bulk_update_sync_status(ids, need_sync)
        else:
            current_app.logger.warning(f"DAO for {self.dao.model_class.__name__} does not support bulk_update_sync_status.")
            return 0 