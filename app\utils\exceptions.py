class AppException(Exception):
    """应用基础异常类"""
    
    def __init__(self, message, status_code=400, payload=None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.payload = payload
        
    def to_dict(self):
        """转换为字典表示"""
        result = dict(self.payload or {})
        result['message'] = self.message
        result['status_code'] = self.status_code
        return result
        
class APIException(AppException):
    """API异常类"""
    
    def __init__(self, message, status_code=500, original_exception=None, payload=None):
        super().__init__(message, status_code, payload)
        self.original_exception = original_exception
        
class ValidationException(AppException):
    """数据验证异常类"""
    
    def __init__(self, errors, message="数据验证失败"):
        super().__init__(message, status_code=400, payload={'errors': errors})