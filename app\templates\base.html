<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>{% block title %}数据处理系统{% endblock %}</title>
    {% block styles %}
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dataTables.bootstrap4.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.bootstrap4.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/buttons.bootstrap4.min.css') }}">
    <!-- 自定义样式 -->
    <style>
        body {
            padding-top: 60px;
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .footer {
            margin-top: 45px;
            padding: 15px 0;
            border-top: 1px solid #e5e5e5;
            color: #777;
        }
        .dataTables_wrapper .dataTables_length {
            margin-bottom: 15px;
        }
        .column-toggle-dropdown {
            margin-bottom: 15px;
        }
        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-group-sm {
            margin-right: 5px;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
    </style>
    {% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-md navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">数据处理系统</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarCollapse">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <ul class="navbar-nav mr-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/module_operate/">数据操作表管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/llm_ai_data/">LLM AI 数据管理</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <div class="container-fluid">
        {% block content %}{% endblock %}
    </div>

    <!-- 弹窗提示组件 -->
    <div class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-delay="3000">
        <div class="toast-header">
            <strong class="mr-auto" id="toast-title">提示</strong>
            <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="toast-body" id="toast-message">
            操作成功！
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p class="text-center">数据处理系统 &copy; 2023</p>
        </div>
    </footer>

    {% block scripts %}
    <!-- jQuery, Popper.js, Bootstrap JS -->
    <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/popper.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.min.js') }}"></script>
    
    <!-- DataTables JS -->
    <script src="{{ url_for('static', filename='js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js//dataTables.responsive.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js//responsive.bootstrap4.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js//dataTables.buttons.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js//buttons.bootstrap4.min.js') }}"></script>
    
    <!-- 自定义工具函数 -->
    <script>
    // 显示弹窗提示
    function showToast(title, message, type) {
        $('#toast-title').text(title || '提示');
        $('#toast-message').text(message);
        
        // 设置样式
        $('.toast').removeClass('bg-success bg-danger bg-warning bg-info');
        if (type) {
            $('.toast').addClass('bg-' + type);
        }
        
        $('.toast').toast('show');
    }
    </script>
    {% endblock %}
</body>
</html>