from app.extensions import db
from sqlalchemy.exc import IntegrityError
from flask import current_app
import sqlalchemy.sql.expression as sql
from sqlalchemy import and_, or_, not_
import datetime
from sqlalchemy import text # For sql.false() or similar
import decimal # For Numeric types
from sqlalchemy.types import Integer, Float, Double, Numeric, String, Boolean, DateTime, Date, Time

class BaseDAO:
    """通用数据访问对象基类"""

    def __init__(self, model_class):
        """
        初始化DAO

        Args:
            model_class: 与此DAO关联的SQLAlchemy模型类
        """
        self.model_class = model_class
        
        # 在运行时才检查模型类是否有query属性，而不是在初始化时
        # 这有助于避免应用上下文错误
        try:
            if not hasattr(model_class, '__tablename__'):
                current_app.logger.warning(f"Warning: Provided model_class '{model_class.__name__}' may not be a valid SQLAlchemy model (missing __tablename__).")
        except Exception as e:
            current_app.logger.warning(f"Unable to fully validate model_class '{model_class.__name__}' during initialization: {str(e)}")
            
        current_app.logger.debug(f"BaseDAO initialized for model: {self.model_class.__name__}")

    def get_by_id(self, id):
        """根据ID获取记录 (包括已逻辑删除的)"""
        current_app.logger.debug(f"Getting {self.model_class.__name__} by id: {id}")
        return self.model_class.query.filter_by(id=id).first()

    def get_active_by_id(self, id):
        """根据ID获取未逻辑删除的记录"""
        current_app.logger.debug(f"Getting active {self.model_class.__name__} by id: {id}")
        return self.model_class.query.filter_by(id=id, is_deleted=0).first()

    def create(self, data):
        """创建新记录"""
        current_app.logger.debug(f"Creating new {self.model_class.__name__} with data: {data}")
        try:
            model = self.model_class(**data)
            db.session.add(model)
            db.session.commit()
            current_app.logger.info(f"Successfully created {self.model_class.__name__} with id: {model.id}")
            return model
        except IntegrityError as e:
            db.session.rollback()
            current_app.logger.error(f"IntegrityError creating {self.model_class.__name__}: {e}")
            return None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Exception creating {self.model_class.__name__}: {e}")
            raise # Re-raise other exceptions

    def update(self, id, data):
        """更新记录"""
        current_app.logger.debug(f"Updating {self.model_class.__name__} id {id} with data: {data}")
        model = self.get_by_id(id) # Get potentially deleted record to allow undelete via update
        if model:
            try:
                for key, value in data.items():
                    if hasattr(model, key):
                        setattr(model, key, value)
                    else:
                        current_app.logger.warning(f"Attempted to update non-existent attribute '{key}' on {self.model_class.__name__} id {id}")
                # Ensure update_time is set if model has it (common pattern)
                if hasattr(model, 'update_time'):
                    model.update_time = datetime.datetime.now()

                db.session.commit()
                current_app.logger.info(f"Successfully updated {self.model_class.__name__} id {id}")
                return model
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Exception updating {self.model_class.__name__} id {id}: {e}")
                raise
        else:
            current_app.logger.warning(f"Attempted to update non-existent {self.model_class.__name__} id {id}")
            return None

    def delete(self, id):
        """逻辑删除记录"""
        current_app.logger.debug(f"Logically deleting {self.model_class.__name__} id {id}")
        if not hasattr(self.model_class, 'is_deleted'):
             current_app.logger.error(f"Model {self.model_class.__name__} does not support logical delete (missing 'is_deleted' attribute).")
             return False

        model = self.get_active_by_id(id) # Only delete active records
        if model:
            try:
                model.is_deleted = 1
                # Ensure update_time is set if model has it
                if hasattr(model, 'update_time'):
                    model.update_time = datetime.datetime.now()
                # Potentially update 'update_by' as well
                if hasattr(model, 'update_by') and 'current_user' in current_app.extensions: # Basic check for flask_login
                    from flask_login import current_user
                    if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
                         setattr(model, 'update_by', current_user.username)

                db.session.commit()
                current_app.logger.info(f"Successfully logically deleted {self.model_class.__name__} id {id}")
                return True
            except Exception as e:
                 db.session.rollback()
                 current_app.logger.error(f"Exception deleting {self.model_class.__name__} id {id}: {e}")
                 raise
        else:
            current_app.logger.warning(f"Attempted to delete non-existent or already deleted {self.model_class.__name__} id {id}")
            return False

    def bulk_delete(self, ids):
        """批量逻辑删除记录"""
        current_app.logger.debug(f"Bulk logically deleting {self.model_class.__name__} ids: {ids}")
        if not ids:
            return 0

        if not hasattr(self.model_class, 'is_deleted'):
             current_app.logger.error(f"Model {self.model_class.__name__} does not support logical delete (missing 'is_deleted' attribute).")
             return 0

        try:
            update_values = {self.model_class.is_deleted: 1}
            # Ensure update_time is set if model has it
            if hasattr(self.model_class, 'update_time'):
                update_values[self.model_class.update_time] = datetime.datetime.now()
             # Potentially update 'update_by' as well
            if hasattr(self.model_class, 'update_by') and 'current_user' in current_app.extensions:
                 from flask_login import current_user
                 if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
                    update_values[self.model_class.update_by] = current_user.username

            count = self.model_class.query.filter(
                self.model_class.id.in_(ids),
                self.model_class.is_deleted == 0 # Only delete active records
            ).update(
                update_values,
                synchronize_session=False # More efficient for bulk updates
            )
            db.session.commit()
            current_app.logger.info(f"Successfully bulk logically deleted {count} {self.model_class.__name__} records.")
            return count
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Exception during bulk delete for {self.model_class.__name__}: {e}")
            raise

    def get_all_query(self, sort_by='id', order='asc', search_value=None, searchable_fields=None, advanced_filter=None, include_deleted=False):
        """
        构建查询对象，支持排序、搜索和筛选。

        Args:
            sort_by (str): 用于排序的字段名。
            order (str): 排序方向 ('asc' or 'desc')。
            search_value (str): 搜索关键词。
            searchable_fields (list): 可用于搜索的字段名列表。
            advanced_filter (dict): 高级筛选条件JSON对象。
            include_deleted (bool): 是否包含已逻辑删除的记录。

        Returns:
            Query: SQLAlchemy查询对象。
        """
        current_app.logger.debug(f"Building query for {self.model_class.__name__}: sort_by={sort_by}, order={order}, search={search_value is not None}, advanced_filter={advanced_filter is not None}, include_deleted={include_deleted}")

        # 开始基本查询
        query = self.model_class.query

        # 处理逻辑删除筛选
        if not include_deleted:
            if hasattr(self.model_class, 'is_deleted'):
                query = query.filter(self.model_class.is_deleted == 0)
            else:
                current_app.logger.warning(f"Model {self.model_class.__name__} requested without deleted, but has no 'is_deleted' attribute.")

        # 支持简单文本搜索 (在指定字段上进行 OR 搜索)
        if search_value and searchable_fields:
            search_filters = []
            for field_name in searchable_fields:
                if hasattr(self.model_class, field_name):
                    field = getattr(self.model_class, field_name)
                    # Ensure the column type supports LIKE operations (usually strings)
                    try:
                        field_type = field.property.columns[0].type
                        if isinstance(field_type, String):
                             search_filters.append(field.ilike(f'%{search_value}%')) # Case-insensitive LIKE
                        else:
                             current_app.logger.warning(f"Search requested on non-string field '{field_name}' for model {self.model_class.__name__}, skipping.")
                    except Exception as e:
                         current_app.logger.error(f"Error determining type for searchable field '{field_name}': {e}")
                else:
                    current_app.logger.warning(f"Search requested on non-existent field '{field_name}' for model {self.model_class.__name__}")

            if search_filters:
                query = query.filter(or_(*search_filters))
                current_app.logger.debug(f"Applied search filter: {or_(*search_filters)}")

        # 支持高级筛选
        if advanced_filter and isinstance(advanced_filter, dict):
            try:
                 query = self._apply_advanced_filter(query, advanced_filter)
            except Exception as e:
                 current_app.logger.error(f"Error applying advanced filter: {e}", exc_info=True)
                 # Optionally, decide whether to proceed with a potentially incorrect filter or stop
                 # For now, we log the error and let the query continue without the failed filter part.

        # 支持排序
        if hasattr(self.model_class, sort_by):
            sort_column = getattr(self.model_class, sort_by)
            # Handle potential None values in sorting, placing them last by default
            if order.lower() == 'desc':
                 query = query.order_by(sort_column.desc().nullslast())
            else:
                 query = query.order_by(sort_column.asc().nullslast())
            current_app.logger.debug(f"Applied sorting: {sort_by} {order.lower()}")
        else:
             # Default sort by ID if requested sort_by is invalid
             current_app.logger.warning(f"Invalid sort_by field '{sort_by}' for model {self.model_class.__name__}, defaulting to 'id asc'.")
             query = query.order_by(self.model_class.id.asc())


        # 记录最终SQL查询语句 (不含分页) - 注意：这可能非常消耗性能，尤其是在生产中
        if current_app.config.get('SQLALCHEMY_ECHO', False) or current_app.debug: # Only log if ECHO is on or in debug
            try:
                # 获取原始SQL语句和参数
                sql_stmt = str(query.statement.compile(
                    compile_kwargs={"literal_binds": True} # Use with caution in production!
                ))
                current_app.logger.debug(f"Generated SQL query (before pagination): {sql_stmt}")
            except Exception as e:
                current_app.logger.error(f"Error compiling/logging SQL query: {e}")

        return query

    def get_paginated(self, page=1, per_page=20, **kwargs):
        """获取分页记录"""
        current_app.logger.debug(f"Getting paginated {self.model_class.__name__}: page={page}, per_page={per_page}, kwargs={kwargs}")
        query = self.get_all_query(**kwargs)

        # 应用分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        return pagination


    def _apply_advanced_filter(self, query, filter_data):
        """应用高级筛选条件到查询对象"""
        current_app.logger.debug(f"[_apply_advanced_filter] Applying advanced filter: {filter_data}")
        if not filter_data or not isinstance(filter_data, dict):
            return query

        # Get top-level groups and the operators BETWEEN them
        groups_json = filter_data.get('groups', [])
        operators = filter_data.get('operators', []) # List of 'AND' or 'OR' strings
        current_app.logger.debug(f"[_apply_advanced_filter] Received groups: {len(groups_json)}, operators: {operators}")

        if not groups_json:
            return query

        # Build conditions for each top-level group
        group_conditions = []
        current_app.logger.debug(f"[_apply_advanced_filter] Processing {len(groups_json)} top level groups...")
        for i, group_json in enumerate(groups_json):
            current_app.logger.debug(f"[_apply_advanced_filter]  Processing top level group {i}: {group_json}")
            # Ensure each group has a type for _build_filter_condition
            if 'type' not in group_json:
                group_json['type'] = 'group'

            condition = self._build_filter_condition(group_json)
            # Check if condition is not None and not literally sql.true() or sql.false() unless it's the only condition
            # Simplified: Check if it's not None. SQLAlchemy might optimize True/False.
            if condition is not None:
                 current_app.logger.debug(f"[_apply_advanced_filter]  Condition for group {i}: {condition}")
                 group_conditions.append(condition)
            else:
                current_app.logger.debug(f"[_apply_advanced_filter]  Group {i} resulted in None condition.")

        # If no valid conditions generated, return original query
        if not group_conditions:
            current_app.logger.debug(f"[_apply_advanced_filter] No valid group conditions generated.")
            return query

        # Combine the group conditions using the specified operators sequentially, respecting AND precedence
        final_condition = None
        if len(group_conditions) == 1:
            final_condition = group_conditions[0]
            current_app.logger.debug(f"[_apply_advanced_filter] Applying single group condition: {final_condition}")
        elif len(group_conditions) > 1:
            if len(operators) == len(group_conditions) - 1:
                 # Process with AND having higher precedence than OR
                 or_parts = []
                 current_and_part = [group_conditions[0]]

                 for i in range(len(operators)):
                     op_str = operators[i].upper()
                     next_condition = group_conditions[i+1]

                     if op_str == 'AND':
                         current_and_part.append(next_condition)
                         current_app.logger.debug(f"[_apply_advanced_filter] Adding to AND part: {next_condition}")
                     elif op_str == 'OR':
                         if len(current_and_part) > 0: # Only add if the AND part has something
                              anded_condition = and_(*current_and_part) if len(current_and_part) > 1 else current_and_part[0]
                              or_parts.append(anded_condition)
                              current_app.logger.debug(f"[_apply_advanced_filter] Finalized AND part: {anded_condition}")
                         current_and_part = [next_condition] # Start new AND part
                         current_app.logger.debug(f"[_apply_advanced_filter] Starting new AND part after OR: {next_condition}")
                     else:
                         current_app.logger.warning(f"Invalid top-level operator '{operators[i]}', treating as AND.")
                         current_and_part.append(next_condition) # Treat unknown as AND

                 # After loop, finalize the last AND part
                 if len(current_and_part) > 0:
                     last_anded_condition = and_(*current_and_part) if len(current_and_part) > 1 else current_and_part[0]
                     or_parts.append(last_anded_condition)
                     current_app.logger.debug(f"[_apply_advanced_filter] Finalized last AND part: {last_anded_condition}")

                 # Combine all OR parts
                 if not or_parts:
                     current_app.logger.error("[_apply_advanced_filter] No OR parts generated despite multiple groups? Check logic.")
                     # Decide fallback: maybe AND all original conditions? Or return False?
                     # Let's be conservative and AND original conditions as a fallback
                     final_condition = and_(*group_conditions)
                     current_app.logger.debug(f"[_apply_advanced_filter] Final condition (fallback AND): {final_condition}")
                 elif len(or_parts) == 1:
                     final_condition = or_parts[0]
                     current_app.logger.debug(f"[_apply_advanced_filter] Final condition is single OR part: {final_condition}")
                 else:
                     final_condition = or_(*or_parts)
                     current_app.logger.debug(f"[_apply_advanced_filter] Final condition combines OR parts: {final_condition}")

            else:
                 current_app.logger.error(f"Mismatch between number of groups ({len(group_conditions)}) and operators ({len(operators)}) at top level. Defaulting to AND between all.")
                 final_condition = and_(*group_conditions)
                 current_app.logger.debug(f"[_apply_advanced_filter] Final combined condition (fallback AND): {final_condition}")

        # Apply the final combined condition if it exists and is not equivalent to TRUE
        # Checking for exactly sql.true() might be tricky due to object identity.
        # We apply if final_condition is not None. SQLAlchemy might optimize.
        if final_condition is not None:
             current_app.logger.debug(f"[_apply_advanced_filter] Applying final filter: {final_condition}")
             # Check if the condition is always false (e.g., empty group results)
             # This check is basic and might not catch all cases
             if str(final_condition) == str(sql.false()):
                 current_app.logger.warning("Advanced filter resulted in an always-false condition.")
                 # Return a query that yields no results
                 return query.filter(sql.false())
             else:
                 return query.filter(final_condition)
        else:
             current_app.logger.debug("[_apply_advanced_filter] No final condition to apply.")
             return query # Return original query if no condition generated

    def _build_filter_condition(self, filter_data):
        """从JSON数据递归构建筛选条件"""
        current_app.logger.debug(f"[_build_filter_condition] Processing filter data: {filter_data}")
        if not filter_data or not isinstance(filter_data, dict):
            return None

        filter_type = filter_data.get('type')

        # --- 处理组 (Recursive Case) ---
        if filter_type == 'group':
            conditions = []
            group_operator = filter_data.get('operator', 'AND').upper()
            if group_operator not in ['AND', 'OR']:
                current_app.logger.warning(f"Invalid group operator '{group_operator}', defaulting to 'AND'.")
                group_operator = 'AND'
            current_app.logger.debug(f"[_build_filter_condition] Group (Operator: {group_operator})")

            for item in filter_data.get('conditions', []):
                condition = self._build_filter_condition(item) # Recursive call
                if condition is not None:
                    # Avoid adding always-true conditions unless it's the only one
                    # Simplified: Add all non-None conditions for now
                    conditions.append(condition)
                    current_app.logger.debug(f"[_build_filter_condition]  Added sub-condition: {condition}")
                else:
                     current_app.logger.debug(f"[_build_filter_condition]  Skipped None sub-condition from item: {item}")

            if not conditions:
                current_app.logger.debug(f"[_build_filter_condition] Group resulted in no valid sub-conditions, returning None.")
                return None # No conditions in this group

            # Combine conditions using the group operator
            if len(conditions) == 1:
                 final_group_condition = conditions[0] # No need for and_() or or_() for single condition
            elif group_operator == 'AND':
                 final_group_condition = and_(*conditions)
            else: # OR
                 final_group_condition = or_(*conditions)

            current_app.logger.debug(f"[_build_filter_condition] Returning combined group condition: {final_group_condition}")
            return final_group_condition

        # --- 处理条件 (Base Case) ---
        elif filter_type == 'condition':
            field_name = filter_data.get('field')
            operator = filter_data.get('operator')
            value = filter_data.get('value') # Raw value from JSON
            current_app.logger.debug(f"[_build_filter_condition] Condition: Field={field_name}, Op={operator}, RawValue={value}")

            if not field_name or not operator:
                current_app.logger.warning(f"Skipping condition due to missing field or operator: {filter_data}")
                return None # Invalid condition data

            if not hasattr(self.model_class, field_name):
                current_app.logger.error(f"Advanced filter error: Field '{field_name}' not found on model {self.model_class.__name__}.")
                return None # Field doesn't exist

            field = getattr(self.model_class, field_name)

            # Handle operators that don't use value first
            if operator == 'is_null':
                return field.is_(None)
            if operator == 'is_not_null':
                return field.isnot(None)

            # Determine field type for conversion
            try:
                column = field.property.columns[0]
                field_type = column.type
                current_app.logger.debug(f"Field '{field_name}' type determined as: {field_type}")
            except Exception as e:
                current_app.logger.error(f"Could not determine type for field '{field_name}': {e}")
                return None # Cannot process condition without type info

            # Process and convert value based on field type
            processed_value = None
            is_value_considered_null = (value is None) # Treat only Python None as null initially
            if isinstance(value, str) and value.strip() == '':
                 is_value_considered_null = True # Treat empty string as null as well

            if is_value_considered_null:
                 # Handle operators comparing with NULL explicitly
                 if operator == 'eq': return field.is_(None)
                 if operator == 'neq': return field.isnot(None)
                 # Other comparisons with NULL are typically false in SQL
                 current_app.logger.debug(f"Operator '{operator}' with NULL/empty value for field '{field_name}' results in FALSE.")
                 return sql.false() # Effectively FALSE

            # Value is not null or empty string, proceed with type conversion
            try:
                if isinstance(field_type, Boolean):
                    if isinstance(value, str):
                        processed_value = value.lower() in ('true', '1', 'yes', 'y')
                    else:
                        processed_value = bool(value)
                elif isinstance(field_type, DateTime):
                    if isinstance(value, str):
                         # Handle ISO format (potentially with T, Z) and common date formats
                         try: processed_value = datetime.datetime.fromisoformat(value.replace('Z', '+00:00'))
                         except ValueError:
                             try: processed_value = datetime.datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                             except ValueError:
                                 try: # Try just date part
                                     date_part = datetime.date.fromisoformat(value)
                                     processed_value = datetime.datetime.combine(date_part, datetime.time.min)
                                 except ValueError: raise ValueError(f"Cannot parse DateTime string '{value}'")
                    elif isinstance(value, datetime.datetime): processed_value = value
                    elif isinstance(value, datetime.date): processed_value = datetime.datetime.combine(value, datetime.time.min)
                    else: raise TypeError(f"Unsupported type for DateTime conversion: {type(value)}")
                elif isinstance(field_type, Date):
                    if isinstance(value, str):
                         try: processed_value = datetime.date.fromisoformat(value.split('T')[0]) # Take date part if T exists
                         except ValueError: raise ValueError(f"Cannot parse Date string '{value}'")
                    elif isinstance(value, datetime.date): processed_value = value
                    elif isinstance(value, datetime.datetime): processed_value = value.date()
                    else: raise TypeError(f"Unsupported type for Date conversion: {type(value)}")
                elif isinstance(field_type, Time):
                     if isinstance(value, str):
                          try: processed_value = datetime.time.fromisoformat(value)
                          except ValueError: raise ValueError(f"Cannot parse Time string '{value}'")
                     elif isinstance(value, datetime.time): processed_value = value
                     else: raise TypeError(f"Unsupported type for Time conversion: {type(value)}")
                elif isinstance(field_type, Integer):
                    processed_value = int(float(str(value))) # Convert via float for robustness e.g., "10.0"
                elif isinstance(field_type, (Float, Double)):
                    processed_value = float(str(value))
                elif isinstance(field_type, Numeric):
                    processed_value = decimal.Decimal(str(value))
                elif isinstance(field_type, String):
                    processed_value = str(value)
                else:
                    # Fallback for unknown types - treat as string? Or raise error?
                    current_app.logger.warning(f"Unhandled field type '{field_type}' for field '{field_name}', attempting string conversion.")
                    processed_value = str(value)

                current_app.logger.debug(f"Value '{value}' processed to: {processed_value} (type: {type(processed_value)})")

            except (ValueError, TypeError, Exception) as e:
                current_app.logger.error(f"Failed to process/convert value '{value}' for field '{field_name}' (type {field_type}): {e}")
                return None # Indicate conversion failure

            # Apply operator with processed value
            try:
                sql_condition = None
                if operator == 'eq': sql_condition = (field == processed_value)
                elif operator == 'neq': sql_condition = (field != processed_value)
                elif operator == 'gt': sql_condition = (field > processed_value)
                elif operator == 'gte': sql_condition = (field >= processed_value)
                elif operator == 'lt': sql_condition = (field < processed_value)
                elif operator == 'lte': sql_condition = (field <= processed_value)
                # String specific operators
                elif isinstance(field_type, String):
                    if operator == 'contains': sql_condition = field.ilike(f'%{processed_value}%')
                    elif operator == 'not_contains': sql_condition = not_(field.ilike(f'%{processed_value}%'))
                    elif operator == 'starts_with': sql_condition = field.ilike(f'{processed_value}%')
                    elif operator == 'ends_with': sql_condition = field.ilike(f'%{processed_value}')
                    else: current_app.logger.warning(f"Unsupported string operator '{operator}' for field '{field_name}'.")
                # Handle case where non-string operator used on string field (or vice-versa) - potentially log warning
                elif operator in ['contains', 'not_contains', 'starts_with', 'ends_with']:
                     current_app.logger.warning(f"String operator '{operator}' used on non-string field '{field_name}' (type {field_type}). Condition might fail or be incorrect.")
                     return sql.false() # String ops on non-string is likely invalid

                if sql_condition is not None:
                     current_app.logger.debug(f"[_build_filter_condition] Generated SQL condition: {sql_condition}")
                     return sql_condition
                else:
                     current_app.logger.error(f"Unsupported operator '{operator}' or combination for field '{field_name}' (type {field_type}).")
                     return None # Operator not supported or failed

            except Exception as e:
                # Catch errors during comparison (e.g., comparing incompatible types if conversion failed silently)
                current_app.logger.error(f"Error applying operator '{operator}' for field '{field_name}' with value {processed_value}: {e}")
                return None # Indicate failure

        # --- Handle Unknown Type ---
        else:
            current_app.logger.error(f"Unknown filter item type encountered: {filter_type}. Data: {filter_data}")
            return None

    # --- Optional Methods ---

    def update_sort_order(self, id, action):
        """更新排序顺序 (如果模型支持 sort_order)"""
        if not hasattr(self.model_class, 'sort_order'):
            current_app.logger.warning(f"Model {self.model_class.__name__} does not have 'sort_order', cannot update sort order.")
            return False

        current_app.logger.debug(f"Updating sort order for {self.model_class.__name__} id {id}, action: {action}")
        try:
            model = self.get_active_by_id(id) # Only sort active records
            if not model:
                return False

            if action == 'top':
                min_order = db.session.query(db.func.min(self.model_class.sort_order)).filter(
                    self.model_class.is_deleted == 0
                ).scalar()
                # Handle case where table is empty or all sort_orders are NULL
                min_order = min_order if min_order is not None else 0

                # If already at the top or lower, do nothing
                if model.sort_order is not None and model.sort_order <= min_order:
                    current_app.logger.debug(f"Record id {id} is already at the top.")
                    return True

                new_order = min_order - 10 # Simple gap strategy
                model.sort_order = new_order

            elif action == 'bottom':
                max_order = db.session.query(db.func.max(self.model_class.sort_order)).filter(
                    self.model_class.is_deleted == 0
                ).scalar()
                max_order = max_order if max_order is not None else 0

                # If already at the bottom or higher, do nothing
                if model.sort_order is not None and model.sort_order >= max_order:
                     current_app.logger.debug(f"Record id {id} is already at the bottom.")
                     return True

                new_order = max_order + 10
                model.sort_order = new_order
            else:
                 current_app.logger.warning(f"Invalid sort order action: {action}")
                 return False

            # Ensure update_time is set if model has it
            if hasattr(model, 'update_time'):
                 model.update_time = datetime.datetime.now()

            db.session.commit()
            current_app.logger.info(f"Successfully updated sort order for {self.model_class.__name__} id {id}")
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Exception updating sort order for {self.model_class.__name__} id {id}: {e}")
            return False # Indicate failure

    def batch_update_sort_order(self, ids, action):
        """批量更新排序顺序 (如果模型支持 sort_order)"""
        if not hasattr(self.model_class, 'sort_order'):
            current_app.logger.warning(f"Model {self.model_class.__name__} does not have 'sort_order', cannot batch update sort order.")
            return 0
        if not ids:
            return 0

        current_app.logger.debug(f"Batch updating sort order for {self.model_class.__name__} ids {ids}, action: {action}")
        try:
            count = 0
            # Fetch models preserving original order for relative placement
            models = self.model_class.query.filter(
                self.model_class.id.in_(ids),
                self.model_class.is_deleted == 0
            ).order_by(self.model_class.sort_order.asc().nullslast()).all() # Get active records only

            if not models:
                return 0

            if action == 'top':
                min_order = db.session.query(db.func.min(self.model_class.sort_order)).filter(
                    self.model_class.is_deleted == 0
                ).scalar()
                min_order = min_order if min_order is not None else 0

                # Assign decreasing sort orders based on the fetched order
                for i, model in enumerate(models):
                    model.sort_order = min_order - (len(models) - i) * 10 # Maintain relative order
                    if hasattr(model, 'update_time'): model.update_time = datetime.datetime.now()
                    count += 1

            elif action == 'bottom':
                max_order = db.session.query(db.func.max(self.model_class.sort_order)).filter(
                    self.model_class.is_deleted == 0
                ).scalar()
                max_order = max_order if max_order is not None else 0

                # Assign increasing sort orders based on the fetched order
                for i, model in enumerate(models):
                    model.sort_order = max_order + (i + 1) * 10 # Maintain relative order
                    if hasattr(model, 'update_time'): model.update_time = datetime.datetime.now()
                    count += 1
            else:
                 current_app.logger.warning(f"Invalid batch sort order action: {action}")
                 return 0

            db.session.commit()
            current_app.logger.info(f"Successfully batch updated sort order for {count} {self.model_class.__name__} records.")
            # Consider calling _maybe_normalize_sort_order if needed, but keep it simple for now.
            return count
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Exception during batch sort order update for {self.model_class.__name__}: {e}")
            return 0 # Indicate failure

    def update_sync_status(self, id, need_sync=1):
        """更新同步状态 (如果模型支持 need_sync)"""
        if not hasattr(self.model_class, 'need_sync'):
            current_app.logger.warning(f"Model {self.model_class.__name__} does not have 'need_sync', cannot update sync status.")
            return False

        current_app.logger.debug(f"Updating sync status for {self.model_class.__name__} id {id} to {need_sync}")
        model = self.get_active_by_id(id)
        if model:
            try:
                model.need_sync = need_sync
                if hasattr(model, 'update_time'): model.update_time = datetime.datetime.now()
                db.session.commit()
                current_app.logger.info(f"Successfully updated sync status for {self.model_class.__name__} id {id}")
                return True
            except Exception as e:
                db.session.rollback()
                current_app.logger.error(f"Exception updating sync status for {self.model_class.__name__} id {id}: {e}")
                return False
        else:
            current_app.logger.warning(f"Attempted to update sync status for non-existent or deleted {self.model_class.__name__} id {id}")
            return False

    def bulk_update_sync_status(self, ids, need_sync=1):
        """批量更新同步状态 (如果模型支持 need_sync)"""
        if not hasattr(self.model_class, 'need_sync'):
            current_app.logger.warning(f"Model {self.model_class.__name__} does not have 'need_sync', cannot batch update sync status.")
            return 0
        if not ids:
            return 0

        current_app.logger.debug(f"Batch updating sync status for {self.model_class.__name__} ids {ids} to {need_sync}")
        try:
            update_values = {self.model_class.need_sync: need_sync}
            if hasattr(self.model_class, 'update_time'):
                 update_values[self.model_class.update_time] = datetime.datetime.now()

            count = self.model_class.query.filter(
                self.model_class.id.in_(ids),
                self.model_class.is_deleted == 0 # Only update active records
            ).update(
                update_values,
                synchronize_session=False
            )
            db.session.commit()
            current_app.logger.info(f"Successfully batch updated sync status for {count} {self.model_class.__name__} records.")
            return count
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Exception during batch sync status update for {self.model_class.__name__}: {e}")
            return 0 # Indicate failure

    # Note: _maybe_normalize_sort_order from LlmAiOperateDAO is not included here
    # as it's a more specific optimization that might not be universally desired.
    # It could be added later or implemented in specific subclasses if needed.
