"""
Flask应用扩展管理模块

这个模块用于集中管理Flask扩展的实例化，
避免循环导入问题，遵循Flask应用的最佳实践。
"""

from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_bootstrap import Bootstrap

# 创建扩展实例
db = SQLAlchemy()
migrate = Migrate()
bootstrap = Bootstrap()

# 初始化函数
def init_extensions(app):
    """
    初始化所有Flask扩展
    
    Args:
        app: Flask应用实例
    """
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    bootstrap.init_app(app) 