import json
import pymysql
import pymysql.cursors

import os
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量
load_dotenv()

# --- 数据库配置 ---
# !!! 重要: 请将以下信息更新为您的 MySQL 数据库凭据。
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),      # MySQL 服务器 IP/主机名
    'user': os.getenv('DB_USER', 'root'),           # MySQL 用户名
    'password': os.getenv('DB_PASSWORD', ''),       # MySQL 密码
    'db': os.getenv('DB_NAME', 'test'),             # 数据库名称
    'charset': 'utf8mb4',                           # 字符集
    'cursorclass': pymysql.cursors.DictCursor       # 可选: 使查询结果表现得像字典
}
# !!! 重要: 为了让 'INSERT IGNORE' 正确工作并防止重复的
# model_name 条目, 强烈建议在您的 'llm_ai_operate' 表中的 'model_name' 列
# 上创建一个 UNIQUE (唯一) 索引。
# 添加唯一索引的示例 SQL:
# ALTER TABLE llm_ai_operate ADD UNIQUE INDEX model_name_unique (model_name);

def get_db_connection():
    """建立到 MySQL 数据库的连接。"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        return connection
    except pymysql.Error as e:
        print(f"连接 MySQL 数据库时出错: {e}")
        return None

def parse_json_string(json_string_input):
    """
    将 JSON 字符串解析为 Python 对象 (字典或列表)。
    如果解析失败或根不是字典/列表，则返回 None。
    """
    if not isinstance(json_string_input, str):
        print(f"输入错误: parse_json_string 需要一个字符串，但得到了 {type(json_string_input)}")
        return None
    
    cleaned_json_string = json_string_input.strip() # 去除前后空白

    try:
        data = json.loads(cleaned_json_string)
        if isinstance(data, (dict, list)):
            return data
        else:
            print(f"警告: JSON 根不是对象或数组, 而是: {type(data)}。此函数期望 JSON 对象或数组。")
            return None
    except json.JSONDecodeError as e:
        print(f"解码 JSON 时出错: {e} (原始字符串片段: '{cleaned_json_string[:100]}...')")
        return None
    except Exception as e:
        print(f"JSON 解析期间发生意外错误: {e}")
        return None

def _find_values_recursive_visual_layer(current_data, current_visual_depth, target_visual_depth, field_name, results_set):
    """
    根据"视觉层级"递归查找目标字段的值。
    视觉层级定义：
    - 层级1：最外层对象的键，或最外层数组中字典元素的键。
    - 层级N：是层级(N-1)的有效值（该值必须是字典，或者是列表其元素为字典）内部的键。
    - 列表本身不增加视觉层级数。层级是针对"包含目标字段的字典"的嵌套深度而言的。

    参数:
    current_data (any): 当前正在处理的 Python 对象（字典、列表、简单值）。
    current_visual_depth (int): 当前正在考察的"潜在字典容器"的视觉层级。
                                初始调用时，对于根JSON的直接内容，此值为1。
    target_visual_depth (int): 用户期望找到 field_name 的视觉层级。
    field_name (str): 目标字段名。
    results_set (set): 存储提取到的唯一字符串化值的集合。
    """
    if current_data is None:
        return

    # 如果当前视觉层级已超过目标层级，则无需继续深入此路径
    if current_visual_depth > target_visual_depth:
        return

    if isinstance(current_data, dict):
        # 当前 current_data 是一个字典，它正位于 current_visual_depth
        if current_visual_depth == target_visual_depth:
            # 此字典位于目标视觉层级，检查它是否包含目标字段
            if field_name in current_data and current_data[field_name] is not None:
                value_str = str(current_data[field_name])
                print(f"  [调试信息] 捕获于字典 (视觉层级 {current_visual_depth}): 键='{field_name}', 值='{value_str}'")
                results_set.add(value_str)
        
        # 无论当前字典是否为目标层级字典，都需要遍历其值，这些值将构成下一视觉层级的内容
        # （如果当前字典层级已达目标层级，其子项就无需检查是否包含目标字段了，因为目标字段只在目标层级的字典中找，
        # 但子项仍需被遍历，以防子项是列表，列表内又有目标层级的字典）
        # 因此，只要 current_visual_depth 未超过 target_visual_depth，就继续探索其子节点
        # 子节点（字典的值）的视觉层级将是 current_visual_depth + 1
        for key, value_node in current_data.items():
            _find_values_recursive_visual_layer(value_node, current_visual_depth + 1, target_visual_depth, field_name, results_set)

    elif isinstance(current_data, list):
        # 当前 current_data 是一个列表。列表本身不计为一个独立的视觉层级。
        # 我们将遍历其元素，并使用相同的 current_visual_depth 来处理这些元素。
        # 如果元素是字典，该字典将被视为在 current_visual_depth 进行评估。
        # 如果元素是列表，将进一步递归。
        for element_node in current_data:
            _find_values_recursive_visual_layer(element_node, current_visual_depth, target_visual_depth, field_name, results_set)
    
    # 其他简单类型（字符串、数字等）不能作为容器，故不作处理。

def extract_target_values(parsed_json_data, target_level_user, target_field_name):
    """
    从已解析的 JSON 数据中的特定"视觉层级"提取目标字段的值。
    
    参数:
    parsed_json_data (dict or list): 从 JSON 字符串解析得到的 Python 对象。
    target_level_user (int): 用户定义的视觉层级 (1, 2, 或 3等)。
    target_field_name (str): 要提取其值的字段的名称。
    返回:
    一个包含唯一字符串化值的集合。
    """
    collected_values = set()
    if parsed_json_data is None:
        print("无法从 None 中提取值 (parsed_json_data 为 null)。")
        return collected_values

    print(f'开始根据"视觉层级"提取: 目标层级 {target_level_user}, 字段 "{target_field_name}"...')
    # 初始调用时，根JSON的直接内容（无论是字典还是列表中的首层元素）的容器被视为视觉层级 1
    _find_values_recursive_visual_layer(parsed_json_data, 1, target_level_user, target_field_name, collected_values)
    return collected_values

def batch_save_model_names(model_names_set):
    """
    将一组模型名称保存到 'llm_ai_operate' 表中。
    在Python应用层面进行去重：先查询数据库中已存在的值，然后只插入新值。
    保留数据库层面的 `INSERT IGNORE` 作为并发写入等情况的最后防线。
    返回实际插入的新记录数量。
    """
    if not model_names_set:
        print("[数据库操作] 没有提取到模型名称需要保存。")
        return 0

    print(f"\n[数据库操作] 提取到 {len(model_names_set)} 个潜在的模型名称，将进行去重处理。")
    
    connection = get_db_connection()
    if not connection:
        return 0 # 连接失败则无法继续

    existing_names_in_db = set()
    try:
        # 1. 查询数据库中已存在的值
        # 为了避免SQL IN子句过长或参数过多的问题，如果集合很大，理论上应分批查询
        # 但这里假设 model_names_set 通常不会极端大
        if model_names_set: # 只有当有待检查的值时才查询
            with connection.cursor() as cursor:
                # 使用 %s 作为占位符，pymysql 会处理转义
                # 将 set 转换为 tuple 传递给 IN 子句
                placeholders = ', '.join(['%s'] * len(model_names_set))
                sql_check_existence = f"SELECT model_name FROM llm_ai_operate WHERE model_name IN ({placeholders})"
                cursor.execute(sql_check_existence, tuple(model_names_set))
                rows = cursor.fetchall()
                for row in rows:
                    existing_names_in_db.add(row['model_name']) # 假设cursorclass=DictCursor
            
            if existing_names_in_db:
                print(f"[数据库操作] 在数据库中找到了以下 {len(existing_names_in_db)} 个已存在的名称 (将不会重复插入):")
                for name in sorted(list(existing_names_in_db)):
                    print(f"  - 已存在: \"{name}\"")
            else:
                print("[数据库操作] 待检查的名称在数据库中均未找到。")

        # 2. 从待保存集合中剔除已存在的值，得到真正需要插入的新值
        new_names_to_insert_set = model_names_set - existing_names_in_db
        
        if not new_names_to_insert_set:
            print("[数据库操作] 应用层去重后，没有新的模型名称需要插入数据库。")
            return 0

        data_to_insert_tuples = [(name,) for name in sorted(list(new_names_to_insert_set))]
        print(f"[数据库操作] 应用层去重后，准备批量插入以下 {len(data_to_insert_tuples)} 个新的模型名称到数据库:")
        for item_tuple in data_to_insert_tuples:
            print(f"  - 新增待插: \"{item_tuple[0]}\"")

        # 3. 批量插入新值 (仍然使用 INSERT IGNORE 作为最后保障)
        saved_count = 0
        with connection.cursor() as cursor:
            sql_insert = "INSERT IGNORE INTO llm_ai_operate (model_name) VALUES (%s)"
            affected_rows = cursor.executemany(sql_insert, data_to_insert_tuples)
            connection.commit()
            saved_count = affected_rows if affected_rows is not None else 0
            
            if saved_count > 0:
                print(f"[数据库操作] 成功向数据库中插入 {saved_count} 个新的模型名称。")
            else:
                # 如果 affected_rows 是0，但 new_names_to_insert_set 非空，
                # 可能是因为 INSERT IGNORE 由于某些原因（如并发写入后又存在了）再次跳过了。
                print(f"[数据库操作] 尝试插入 {len(data_to_insert_tuples)} 个新识别的名称，但实际插入了 {saved_count} 行。若前者大于0，请检查并发或数据库状态。")
        return saved_count

    except pymysql.Error as e:
        print(f"数据库操作期间出错: {e}")
        if connection:
            try: connection.rollback() 
            except pymysql.Error as re: print(f"回滚期间出错: {re}")
        return 0 # 返回0表示没有成功保存新记录
    except Exception as ex:
        print(f"数据库操作期间发生意外错误: {ex}")
        if connection:
            try: connection.rollback()
            except pymysql.Error as rex: print(f"回滚期间出错: {rex}")
        return 0
    finally:
        if connection:
            connection.close()

def process_json_and_save_to_db(json_string_input, target_layer, target_field_key):
    """
    主函数，用于解析 JSON 字符串，从目标"视觉层级"提取指定字段值，
    打印它们，并将唯一的、数据库中尚不存在的新值批量保存到数据库。
    """
    print(f"开始处理JSON: 目标视觉层级 {target_layer}, 目标字段 '{target_field_key}'...")
    
    parsed_data = parse_json_string(json_string_input)
    if parsed_data is None:
        print("由于 JSON 解析问题，处理中止。")
        print("=" * 50 + "\n")
        return

    extracted_names = extract_target_values(parsed_data, target_layer, target_field_key)
    
    if not extracted_names:
        print(f'未能根据"视觉层级" {target_layer} 和字段名 {target_field_key} 找到任何值。')
    else:
        print(f'根据"视觉层级"提取到 {len(extracted_names)} 个唯一的模型名称值:')
        for name_idx, name_val in enumerate(sorted(list(extracted_names))):
            print(f'  提取值 #{name_idx + 1}: "{name_val}"')
        # 详细的待保存列表打印已移至 batch_save_model_names 内部
        print("-" * 30)

    num_saved = batch_save_model_names(extracted_names)
    print(f'数据库保存操作完成。共向数据库成功插入了 {num_saved} 条新的记录。详情请查看以上日志。')
    print('=' * 50 + '\n')


# --- 示例用法和测试用例 ---
if __name__ == "__main__":
    # 运行前请确保:
    # 1. pymysql 已安装: pip install pymysql
    # 2. python-dotenv 已安装 (如果使用 .env 文件): pip install python-dotenv
    # 3. 如果使用 .env 文件，请确保它与此脚本在同一目录或正确配置路径，
    #    并且包含 DB_HOST, DB_USER, DB_PASSWORD, DB_NAME 等变量。
    # 4. 或者，直接在此脚本顶部的 DB_CONFIG 字典中更新您的 MySQL 凭据。
    # 5. 您的 MySQL 服务器正在运行且可访问。
    # 6. DB_CONFIG 中指定的数据库已存在。
    # 7. 'llm_ai_operate' 表存在于该数据库中。
    #    表的最小 DDL 示例:
    #    CREATE TABLE IF NOT EXISTS llm_ai_operate (
    #        id INT AUTO_INCREMENT PRIMARY KEY,
    #        model_name VARCHAR(255)
    #        -- 其他可能存在的列...
    #    );
    # 8. 关键步骤 (用于防止重复): 在 'model_name' 列上添加 UNIQUE 索引。
    #    ALTER TABLE llm_ai_operate ADD UNIQUE INDEX model_name_unique (model_name);
    
    print("--- 开始 JSON 提取与数据库保存测试 (已应用应用层去重和视觉层级逻辑) ---")
    print("提醒: 请确保数据库配置正确, pymysql 和 python-dotenv (如使用.env) 已安装,")
    print("并且 'llm_ai_operate' 表已存在且 'model_name' 列有 UNIQUE 索引。\n")

    # 测试用例 1: 简单字典, 视觉层级 1
    print("测试用例 1: 简单字典, 视觉层级 1, 字段 'model_name'")
    json_test_1 = '''  {
        "model_name": "GPT-4-Unique", 
        "version": "1.0",
        "params": {"temperature": 0.7}
    }  ''' 
    process_json_and_save_to_db(json_test_1, 1, "model_name")

    # 测试用例 2: 字典列表, 视觉层级 1 (列表中的字典元素被视为层级1的容器)
    print("测试用例 2: 字典列表, 视觉层级 1, 字段 'model_name'")
    json_test_2 = '''[
        {"id": 1, "model_name": "Claude-3-Opus-Unique", "provider": "Anthropic"},
        {"id": 2, "model_name": "Gemini-Pro-1.5-Unique", "provider": "Google"},
        {"id": 3, "model_name": "GPT-4-Unique"} 
    ]''' 
    process_json_and_save_to_db(json_test_2, 1, "model_name")

    # 测试用例 3: 嵌套字典, 视觉层级 3 (用户指定的)
    # root (L0) -> "configs" (L1 key) -> configs_dict (L1 value, L2 dict-container)
    # -> "primary_model" (L2 key) -> primary_model_dict (L2 value, L3 dict-container)
    # -> "model_name" (L3 key in primary_model_dict)
    print("测试用例 3: 嵌套字典, 视觉层级 3, 字段 'model_name'")
    json_test_3 = '''{
        "project": "Alpha",
        "configs": {
            "primary_model": {"model_name": "LLaMA-3-70B-Visual", "quantization": "none"},
            "secondary_model": {"model_name": "Mistral-Large-2-Visual", "quantization": "4bit"}
        },
        "status": "active"
    }'''
    process_json_and_save_to_db(json_test_3, 3, "model_name")

    # 测试用例 4: 更深层嵌套, 视觉层级 3
    # root (L0) -> "stages" (L1 key) -> stages_list (L1 value) 
    #   -> element_dict_in_stages (L2 dict-container, e.g. {"models_used":...} or {"main_model":...} )
    #     -> "models_used" (L2 key) -> models_used_list (L2 value)
    #       -> dict_in_models_used_list (L3 dict-container, e.g. {"model_name": "ResNet50-v2-Visual"})
    #         -> "model_name" (L3 key)
    #     -> "main_model" (L2 key) -> main_model_dict (L2 value, L3 dict-container)
    #       -> "model_name" (L3 key in main_model_dict)
    print("测试用例 4: 更深层嵌套, 视觉层级 3, 字段 'model_name'")
    json_test_4 = '''{
        "experiment_id": "exp-001",
        "stages": [
            {
                "stage_name": "preprocessing",
                "tools": ["toolA", "toolB"]
            },
            {
                "stage_name": "analysis",
                "models_used": [
                    {"type": "classification", "model_name": "ResNet50-v2-Visual"},
                    {"type": "nlp", "model_name": "BERT-large-cased-Visual"}
                ]
            },
            {
                 "stage_name": "analysis_v2",
                 "main_model": {"model_name": "LLaMA-3-70B-Visual"} 
            }
        ]
    }'''
    process_json_and_save_to_db(json_test_4, 3, "model_name")

    # 测试用例 5: 字段位于不同视觉层级的JSON, 分别针对提取
    # L1: "OuterModelX-Visual"
    # L2: "InnerModelY-Visual" (value of "details" is L2 dict)
    # L3: "DeepInnerModelZ-Visual" (value of "config" is L3 dict)
    print("测试用例 5: 字段位于不同视觉层级, 分别针对提取")
    json_test_5 = '''{
        "model_name": "OuterModelX-Visual",
        "details": {
            "model_name": "InnerModelY-Visual",
            "config": {
                "model_name": "DeepInnerModelZ-Visual"
            }
        }
    }'''
    process_json_and_save_to_db(json_test_5, 1, "model_name")
    process_json_and_save_to_db(json_test_5, 2, "model_name")
    process_json_and_save_to_db(json_test_5, 3, "model_name")

    # 测试视觉层级示例: [{key1:{key2:value2}}] -> key2 是视觉层级2
    print("测试视觉层级示例 1: [{key1:{key2:value2}}], 目标层级 2, 字段 'key2'")
    json_visual_test_1 = '''[
        {
            "key1_a": {"key2": "value2_target_A"}
        },
        {
            "key1_b": [ 
                {"other_key": "ignore"}, 
                {"key2": "value2_target_B"} 
            ]
        }
    ]'''
    process_json_and_save_to_db(json_visual_test_1, 2, "key2")

    # 测试视觉层级示例: {"key1": [{"key2": "v2"}]} -> key2 是视觉层级2
    print("测试视觉层级示例 2: {\"key1\": [{\"key2\": \"v2\"}]}, 目标层级 2, 字段 'key2'")
    json_visual_test_2 = '''{
        "key1_root": [
            {"non_target_key": "ignore"},
            {"key2": "value2_target_C"},
            [{"key2": "value2_target_D_in_nested_list"}] 
        ]
    }'''
    process_json_and_save_to_db(json_visual_test_2, 2, "key2")
    
    print("测试用例 6: 目标视觉层级不存在该字段")
    json_test_6 = '''{"data": {"info": "some_info"}}''' # "info" 在视觉层级2
    process_json_and_save_to_db(json_test_6, 2, "model_name")

    print("测试用例 7: 无效JSON")
    json_test_7 = '''{"data": {"info": "some_info"}'''
    process_json_and_save_to_db(json_test_7, 2, "model_name")

    print("测试用例 8: 空JSON对象 {}")
    json_test_8 = '''{}'''
    process_json_and_save_to_db(json_test_8, 1, "model_name") 

    print("测试用例 9: 空JSON数组 []")
    json_test_9 = '''[]'''
    process_json_and_save_to_db(json_test_9, 1, "model_name") 

    print("测试用例 10: 目标层级对于JSON结构过深")
    json_test_10 = '''{"model_name": "Level1OnlyModel"}'''
    process_json_and_save_to_db(json_test_10, 2, "model_name") 

    print("测试用例 11: 目标字段值为数字和布尔型 (应字符串化)")
    json_test_11 = '''[
        {"id": 1, "model_name": 12345},
        {"id": 2, "model_name": true},
        {"id": 3, "model_name": 67.89},
        {"id": 4, "model_name": "Test String"}
    ]'''
    process_json_and_save_to_db(json_test_11, 1, "model_name")

    print("测试用例 12: 目标字段值为null (应跳过)")
    json_test_12 = '''{"model_name": null, "other_key": "some_value"}'''
    process_json_and_save_to_db(json_test_12, 1, "model_name") 

    print("测试用例 13: JSON根不是对象/数组")
    json_test_13_str = '''"this is just a string"'''
    json_test_13_num = '''12345'''
    process_json_and_save_to_db(json_test_13_str, 1, "model_name") 
    process_json_and_save_to_db(json_test_13_num, 1, "model_name") 
    
    print("--- 测试运行结束 ---")
