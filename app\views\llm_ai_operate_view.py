from flask import Blueprint, render_template, request, jsonify, current_app, url_for
from markupsafe import Markup
from app.services.business.llm_ai_operate_service import LlmAiOperateService
import json
import traceback

# 定义简单的分页类，替代flask_paginate
class CustomPagination:
    def __init__(self, items, page, per_page, total):
        self.items = items
        self.page = page
        self.per_page = per_page
        self.total = total
        self.pages = (total + per_page - 1) // per_page if total > 0 else 1
        self.links = self._get_links()
    
    def _get_links(self):
        """生成分页链接HTML"""
        if self.pages <= 1:
            return Markup('')
        
        links = ['<ul class="pagination">']
        
        # 上一页
        if self.page > 1:
            links.append(f'<li class="page-item"><a class="page-link" href="?page={self.page-1}&per_page={self.per_page}">上一页</a></li>')
        else:
            links.append('<li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>')
        
        # 页码
        start_page = max(1, self.page - 2)
        end_page = min(self.pages, start_page + 4)
        
        for p in range(start_page, end_page + 1):
            if p == self.page:
                links.append(f'<li class="page-item active"><a class="page-link" href="#">{p}</a></li>')
            else:
                links.append(f'<li class="page-item"><a class="page-link" href="?page={p}&per_page={self.per_page}">{p}</a></li>')
        
        # 下一页
        if self.page < self.pages:
            links.append(f'<li class="page-item"><a class="page-link" href="?page={self.page+1}&per_page={self.per_page}">下一页</a></li>')
        else:
            links.append('<li class="page-item disabled"><a class="page-link" href="#">下一页</a></li>')
        
        links.append('</ul>')
        return Markup(''.join(links))

module_operate = Blueprint('module_operate', __name__)
service = LlmAiOperateService()

@module_operate.route('/', methods=['GET'])
def index():
    """模型管理首页"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['ITEMS_PER_PAGE'], type=int)
    
    # 获取模型列表
    pagination = service.get_all(page=page, per_page=per_page)
    models = pagination.items
    
    return render_template(
        'llm_ai_operate_template/index.html',
        models=models,
        pagination=pagination
    )

@module_operate.route('/data', methods=['GET'])
def get_data():
    """获取模型数据（API）"""
    try:
        # 获取分页参数
        draw = request.args.get('draw', 1, type=int)
        start = request.args.get('start', 0, type=int)
        length = request.args.get('length', current_app.config['ITEMS_PER_PAGE'], type=int)
        
        # 如果length为-1，则表示"全部"
        if length == -1:
            # 使用一个很大的值，实际上是获取所有数据
            length = 1000000  # 或者其他足够大的数字
        
        # 计算页码
        page = (start // length) + 1 if length > 0 else 1
        
        # 获取排序参数
        order_column = request.args.get('order[0][column]', '2')  # 默认为sort_order列
        order_dir = request.args.get('order[0][dir]', 'asc')
        columns = ['id', 'model_name', 'sort_order', 'need_sync', 'create_time', 'update_time']
        sort_by = columns[int(order_column)] if order_column.isdigit() and int(order_column) < len(columns) else 'sort_order'
        
        # 获取搜索参数
        search_value = request.args.get('search[value]', '')
        
        # 高级筛选参数
        advanced_filter = request.args.get('advanced_filter')
        filter_data = None
        
        # 如果有高级筛选条件，记录详细日志
        if advanced_filter:
            try:
                # 解析JSON字符串（如果是字符串）
                if isinstance(advanced_filter, str):
                    filter_data = json.loads(advanced_filter)
                else:
                    filter_data = advanced_filter
                
                # 验证筛选条件结构
                if 'groups' in filter_data:
                    current_app.logger.info(f"应用高级筛选条件: {json.dumps(filter_data, indent=2, ensure_ascii=False)}")
                else:
                    current_app.logger.warning("高级筛选条件格式不正确，没有找到'groups'字段")
            except json.JSONDecodeError as e:
                current_app.logger.error(f"解析高级筛选条件时出错: {str(e)}")
        
        # 获取模型列表
        result = service.get_all(
            page=page, 
            per_page=length,
            sort_by=sort_by,
            order=order_dir,
            search_value=search_value,  # 传递搜索值
            advanced_filter=filter_data,  # 直接传递高级筛选条件
            **{}
        )
        
        # 构建返回数据
        return jsonify({
            'draw': draw,
            'recordsTotal': result['total'],
            'recordsFiltered': result['total'],
            'data': result['data']
        })
    except Exception as e:
        current_app.logger.error(f"获取数据时出错: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'error': str(e),
            'draw': request.args.get('draw', 1),
            'recordsTotal': 0,
            'recordsFiltered': 0,
            'data': []
        }), 500

@module_operate.route('/create', methods=['POST'])
def create():
    """创建模型（API）"""
    data = request.json
    
    if not data or 'model_name' not in data:
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400
    
    # 检查是否已存在
    existing = service.get_by_model_name(data['model_name'])
    if existing:
        return jsonify({'success': False, 'message': '模型名称已存在'}), 400
    
    # 创建模型
    model = service.create(data)
    if model:
        return jsonify({'success': True, 'model': model.to_dict()})
    else:
        return jsonify({'success': False, 'message': '创建失败'}), 500

@module_operate.route('/update/<int:id>', methods=['PUT'])
def update(id):
    """更新模型（API）"""
    data = request.json
    
    if not data:
        return jsonify({'success': False, 'message': '缺少更新数据'}), 400
    
    # 更新模型
    model = service.update(id, data)
    if model:
        return jsonify({'success': True, 'model': model.to_dict()})
    else:
        return jsonify({'success': False, 'message': '更新失败，记录不存在'}), 404

@module_operate.route('/delete/<int:id>', methods=['DELETE'])
def delete(id):
    """删除模型（API）"""
    # 删除模型
    if service.delete(id):
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': '删除失败，记录不存在'}), 404

@module_operate.route('/batch', methods=['POST'])
def batch_action():
    """批量操作"""
    try:
        data = request.json
        if not data or 'ids' not in data or 'action' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        ids = data.get('ids', [])
        action = data.get('action')
        
        if not ids:
            return jsonify({
                'success': False,
                'message': '未提供操作ID'
            }), 400
        
        # 根据不同的操作调用不同的服务方法
        count = 0
        if action == 'delete':
            # 批量删除
            count = service.bulk_delete(ids)
        elif action == 'top':
            # 批量置顶
            count = service.batch_update_sort_order(ids, 'top')
        elif action == 'bottom':
            # 批量置底
            count = service.batch_update_sort_order(ids, 'bottom')
        elif action == 'sync':
            # 批量标记同步
            count = service.bulk_update_sync_status(ids, 1)
        elif action == 'unsync':
            # 批量取消同步
            count = service.bulk_update_sync_status(ids, 0)
        else:
            return jsonify({
                'success': False,
                'message': f'不支持的操作: {action}'
            }), 400
        
        return jsonify({
            'success': True,
            'count': count,
            'message': f'操作成功，影响{count}条记录'
        })
    except Exception as e:
        current_app.logger.error(f"批量操作出错: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }), 500

@module_operate.route('/sync_status/<int:id>', methods=['PUT'])
def update_sync_status(id):
    """更新同步状态（API）"""
    data = request.json
    
    if not data or 'need_sync' not in data:
        return jsonify({'success': False, 'message': '缺少同步状态参数'}), 400
    
    need_sync = int(data['need_sync'])
    
    if service.update_sync_status(id, need_sync):
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': '更新同步状态失败，记录不存在'}), 404

@module_operate.route('/fetch_models', methods=['POST'])
def fetch_models():
    """从外部API获取模型数据"""
    try:
        data = request.json
        api_base = data.get('api_base')
        api_key = data.get('api_key')
        
        if not api_base or not api_key:
            return jsonify({
                'success': False,
                'message': '缺少必要参数：API Base或API Key'
            }), 400
        
        models = service.fetch_models(api_base, api_key)
        return jsonify({
            'success': True,
            'models': models
        })
    except Exception as e:
        current_app.logger.error(f"获取模型数据出错: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500

@module_operate.route('/import_models', methods=['POST'])
def import_models():
    """导入模型（API）"""
    data = request.json
    
    if not data or 'models_data' not in data or 'field_name' not in data or 'level_value' not in data:
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400
    
    models_data = data['models_data']
    field_name = data['field_name']
    level_value = data['level_value']
    try:
        # 解析模型数据
        #if isinstance(models_data, str):
        #    models_data = json.loads(models_data)
        
        # 导入模型
        result = service.import_models(models_data, field_name, level_value)
        return jsonify({
            'success': True,
            'result': result
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500