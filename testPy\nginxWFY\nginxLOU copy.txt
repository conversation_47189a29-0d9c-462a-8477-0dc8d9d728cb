        # 使用正则匹配动态处理 /action_page-xxx 的请求
        location ~ ^/action_page-(?<app_name>[^/]+)(?<app_path>/.*)?$ {

            # 处理 OPTIONS 预检请求 - 对接跨域请求
            if ($request_method = OPTIONS) {
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, HEAD, POST, PUT, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' $http_access_control_request_headers always;
                add_header 'Access-Control-Max-Age' 86400 always; # 预检缓存 一天内相同跨域请求无需重复发送 OPTIONS 预检请求（可选）
                add_header 'Content-Type' 'text/plain; charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
            if ($request_method !~ ^(GET|HEAD|POST|PUT|OPTIONS)$) {
                return 444;
            }

            # 通用 CORS 跨域头 - 针对非options请求
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, HEAD, POST, PUT, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' $http_access_control_request_headers always;
            # 安全请求头
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
            add_header X-Frame-Options           "SAMEORIGIN" always;
            add_header X-XSS-Protection          "1; mode=block" always;
            add_header X-Content-Type-Options    "nosniff" always;
            add_header Referrer-Policy           "no-referrer-when-downgrade" always;
            #add_header Content-Security-Policy   "default-src 'self';  connect-src 'self'; script-src 'self'; object-src 'none'; frame-ancestors 'none';" always;
            add_header Permissions-Policy        "geolocation=(), microphone=()" always;

		  # 设置前缀路径
            set $prefix_path "/action_app-$app_name";

            # 关键：禁用上游压缩，确保 sub_filter 能工作
            proxy_set_header Accept-Encoding "";

            # --- 4. 响应体内容替换 ---
            # sub_filter_types 适用于多种文本类型
            sub_filter_types text/plain text/html text/css text/xml application/xml application/json application/javascript application/xhtml+xml image/svg+xml;
            sub_filter_once off; # 确保替换所有匹配项

            # 使用 Lua 动态处理响应内容，因为 sub_filter 不支持变量
            header_filter_by_lua_block {
                -- 存储前缀到 nginx 变量供后续使用
                local uri = ngx.var.uri
                local prefix = uri:match("^/action_page-([^/]+)")
                if prefix then
                    ngx.ctx.url_prefix = ngx.var.app_name
                else
                    ngx.ctx.url_prefix = ""
                end
                -- 清除可能干扰的响应头
                ngx.header["Content-Length"] = nil
            }


            body_filter_by_lua_block {
                local chunk = ngx.arg[1]
                local eof = ngx.arg[2]
                -- local prefix = ngx.ctx.url_prefix
                local prefix = '/action_app-' .. ngx.ctx.url_prefix
                if chunk and prefix ~= "" then
                    -- 收集完整响应体
                    ngx.ctx.buffered = (ngx.ctx.buffered or "") .. chunk
                    if eof then
                        local body = ngx.ctx.buffered
                        -- 1. 保护 WebSocket URL
                        body = body:gsub('(WebSocket%s*%(%s*["\'])ws://', '%1WS_PROTECTED://')
                        body = body:gsub('(WebSocket%s*%(%s*["\'])wss://', '%1WSS_PROTECTED://')
                        -- 2. 处理各种相对路径形式
                        -- 处理 href="/xxx" 和 src="/xxx"
                        body = body:gsub('(href%s*=%s*["\'])/', '%1' .. prefix .. '/')
                        body = body:gsub('(src%s*=%s*["\'])/', '%1' .. prefix .. '/')
                        body = body:gsub('(action%s*=%s*["\'])/', '%1' .. prefix .. '/')
                        -- 处理 url("/xxx") 和 url('/xxx')
                        body = body:gsub('(url%s*%(%s*["\'])/', '%1' .. prefix .. '/')
                        body = body:gsub('(url%s*%()/', '%1' .. prefix .. '/')
                        -- 处理 fetch("/xxx") 和类似的 JavaScript 调用
                        body = body:gsub('(fetch%s*%(%s*["\'])/', '%1' .. prefix .. '/')
                        body = body:gsub('(%.get%s*%(%s*["\'])/', '%1' .. prefix .. '/')
                        body = body:gsub('(%.post%s*%(%s*["\'])/', '%1' .. prefix .. '/')
                        body = body:gsub('(%.ajax%s*%(%s*{%s*url%s*:%s*["\'])/', '%1' .. prefix .. '/')
                        -- 处理 JavaScript 中的路径
                        body = body:gsub('(["\']%s*%+%s*["\'])/', '%1' .. prefix .. '/')
                        -- 3. 处理无前导斜杠的相对路径
                        local assets = {'js/', 'javascript/', 'css/', 'style/', 'images/', 'img/', 'assets/', 'static/', 'fonts/', 'api/', 'data/'}
                        for _, asset in ipairs(assets) do
                            body = body:gsub('(href%s*=%s*["\'])' .. asset, '%1' .. prefix .. '/' .. asset)
                            body = body:gsub('(src%s*=%s*["\'])' .. asset, '%1' .. prefix .. '/' .. asset)
                            body = body:gsub('(url%s*%(%s*["\'])' .. asset, '%1' .. prefix .. '/' .. asset)
                            body = body:gsub('(fetch%s*%(%s*["\'])' .. asset, '%1' .. prefix .. '/' .. asset)
                        end
                        -- 4. 处理 base 标签（如果存在）
                        body = body:gsub('(<base%s+href%s*=%s*["\'])([^"\']+)(["\'])', function(pre, url, post)
                            if not url:match('^http') and not url:match('^/action_') then
                                if url:sub(1,1) == '/' then
                                    return pre .. prefix .. url .. post
                                else
                                    return pre .. prefix .. '/' .. url .. post
                                end
                            end
                            return pre .. url .. post
                        end)
                        -- 5. 还原 WebSocket URL
                        body = body:gsub('WS_PROTECTED://', 'ws://')
                        body = body:gsub('WSS_PROTECTED://', 'wss://')
                        -- 6. 清理重复前缀
                        local pattern = prefix:gsub("%-", "%%-")
                        body = body:gsub(pattern .. pattern, prefix)
                        ngx.arg[1] = body
                        ngx.arg[2] = true
                    else
                        ngx.arg[1] = nil
                    end
                end
            }

            # 设置必要的代理头
            proxy_set_header X-Real-IP "";
            proxy_set_header X-Forwarded-For "";
            proxy_set_header X-Forwarded-Host "";
            proxy_set_header Forwarded "";

            proxy_set_header Cf-Connecting-Ip "";
            proxy_set_header Cf-Ipcountry "";
            proxy_set_header True-Client-IP "";
            proxy_set_header CF-RAY "";
            proxy_set_header CF-Visitor "";
            proxy_set_header CF-Request-ID "";
            proxy_set_header X-Forwarded-SSL "";
            proxy_set_header CDN-LOOP "";
            proxy_set_header Referrer "";

            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Original-URI    $current_req;

            proxy_set_header connection        "close";
            proxy_set_header cache-control     "max-age=0";
            proxy_set_header accept-language   $acc_lang;

            proxy_set_header Host   $app_name;
            proxy_set_header Origin $scheme://$app_name;

            # --- 5. 处理后端重定向 (Location header) ---
            # 处理重定向
            proxy_redirect ~^/(.*)$ $prefix_path/$1;
            proxy_redirect ~^https?://[^/]+/(.*)$ $scheme://$host$prefix_path/$1;

            # 根据不同的app_name代理到不同的后端
            if ($app_name = "veloera"){
                proxy_pass http://0.0.0.0:20078$app_path;
            }
        }