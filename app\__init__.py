from flask import Flask, current_app
import os

# 导入扩展管理模块
from app.extensions import db, migrate, bootstrap, init_extensions

# Import base view factory
from app.views.base_view import create_datatable_blueprint
# 仅导入服务类，不立即实例化
from app.services.business.llm_ai_operate_service import LlmAiOperateService
from app.services.business.llm_ai_data_service import LlmAiDataService

def create_app(config):
    """
    创建Flask应用实例
    
    Args:
        config: 应用配置字典，包含所有配置项
               
    Returns:
        Flask应用实例
    """
    app = Flask(__name__)
    
    # 检查配置类型
    if isinstance(config, str):
        # 已废弃：不再支持通过环境名称字符串创建应用
        # 替代方案：从run.py中的get_config()获取配置字典
        raise TypeError(
            "不再支持通过环境名称字符串创建应用。"
            "请使用run.py中的get_config()函数获取配置字典，"
            "并将其传递给create_app函数。"
        )
    
    # 应用配置
    app.config.update(config)
    
    # 如果配置字典中包含init_app方法，调用它
    if 'init_app' in config and callable(config['init_app']):
        config['init_app'](app)
    
    # 初始化扩展
    init_extensions(app)
    
    # 注册蓝图
    from app.views.main import main as main_blueprint
    app.register_blueprint(main_blueprint)
    
    from app.views.llm_ai_operate_view import module_operate as module_operate_blueprint
    app.register_blueprint(module_operate_blueprint, url_prefix='/module_operate')
    
    # --- LLM AI Data (New - Using Generic Factory) ---
    # 1. Define Configuration for llm_ai_data
    LLM_AI_DATA_CONFIG = {
        'module_name': 'llm_ai_data',
        'page_title': 'LLM AI 数据管理',
        'header_title': 'LLM AI 数据列表',
        'template_name': 'shared/generic_datatable_template.html',
        'primaryKey': 'id', # Default primary key field
        # Columns config: Order matters for default sort index mapping!
        # Excludes checkbox and actions column implicitly handled by JS/View factory
        'columns_config': [
            {'data': 'id', 'title': 'ID', 'orderable': True},
            {'data': 'model_name', 'title': '模型名称', 'orderable': True},
            {'data': 'sort_order', 'title': '排序顺序', 'orderable': True},
            {'data': 'need_sync', 'title': '同步状态', 'orderable': True, 'render_func': 'syncStatus'}, # Example using a renderer
            {'data': 'create_time', 'title': '创建时间', 'orderable': True, 'render_func': 'datetime'},
            {'data': 'create_by', 'title': '创建人', 'orderable': True},
            {'data': 'update_time', 'title': '更新时间', 'orderable': True, 'render_func': 'datetime'},
            {'data': 'update_by', 'title': '更新人', 'orderable': True},
            {'data': 'is_deleted', 'title': '状态', 'orderable': True, 'render_func': 'booleanBadge'} # Example renderer
        ],
        'searchable_fields': ['model_name', 'create_by', 'update_by'], # Fields for simple search box
        'filter_fields_config': [
            {'id': 'id', 'name': 'ID', 'type': 'number'},
            {'id': 'model_name', 'name': '模型名称', 'type': 'string'},
            {'id': 'sort_order', 'name': '排序顺序', 'type': 'number'},
            {'id': 'need_sync', 'name': '同步状态', 'type': 'boolean'},
            {'id': 'create_time', 'name': '创建时间', 'type': 'datetime'},
            {'id': 'update_time', 'name': '更新时间', 'type': 'datetime'},
            {'id': 'create_by', 'name': '创建人', 'type': 'string'},
            {'id': 'update_by', 'name': '更新人', 'type': 'string'},
            {'id': 'is_deleted', 'name': '是否删除', 'type': 'boolean'}
        ],
        # Fields for the Add/Edit Modal Form
        'modal_form_fields': [
            {'name': 'model_name', 'label': '模型名称', 'form_type': 'text', 'required': True, 'placeholder': '请输入模型调用名称'},
            {'name': 'sort_order', 'label': '排序顺序', 'form_type': 'number', 'required': False, 'defaultValue': 1},
            {'name': 'need_sync', 'label': '同步状态', 'form_type': 'select', 'required': False, 'defaultValue': 0,
             'options': [
                 {'value': 0, 'text': '无需同步'},
                 {'value': 1, 'text': '需要同步'}
             ]},
            # 'is_deleted' is usually handled by the delete action, not typically in edit form, but can be added if needed.
            # {'name': 'is_deleted', 'label': '标记删除', 'form_type': 'checkbox', 'required': False}
        ],
        'default_sort': ('sort_order', 'asc'), # Default sort column data field and direction
        'enable_batch_operations': {
            'delete': True,
            'top': True,
            'bottom': True,
            'sync': True,
            'unsync': True # Assuming llm_ai_data also uses sync/unsync
        }
        # Optional: Add language_strings, export_file_name etc. if needed
    }

    # 在应用上下文中实例化服务
    with app.app_context():
        # 2. 实例化服务
        llm_ai_data_service = LlmAiDataService()
    
        # 3. 创建并注册蓝图
        llm_ai_data_bp = create_datatable_blueprint(
            module_name='llm_ai_data',
            import_name=__name__ + '_llm_ai_data', # Ensure unique import name
            service_instance=llm_ai_data_service,
            config=LLM_AI_DATA_CONFIG
        )
        app.register_blueprint(llm_ai_data_bp, url_prefix='/llm_ai_data')
    # --- End LLM AI Data ---
    
    # 注册错误处理器
    from app.utils.error_handler import register_error_handlers
    register_error_handlers(app)
    
    return app
