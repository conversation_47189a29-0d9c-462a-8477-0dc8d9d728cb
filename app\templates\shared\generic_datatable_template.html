{% extends "base.html" %}

{% block title %}{{ config.page_title | default('数据管理', true) }}{% endblock %}

{% block styles %}
{{ super() }}
<!-- Font Awesome (如果base.html没有，这里需要确保有) -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<style>
    /* 保持 llm_ai_operate 中的通用样式 */
    .main-content {
        padding: 20px;
    }
    .operation-bar {
        margin-bottom: 20px;
    }
    .column-toggle-dropdown .dropdown-menu {
        padding: 10px;
        min-width: 200px;
    }
    .dataTables_wrapper .dt-buttons {
        margin-bottom: 10px;
    }
    .custom-control {
        margin-bottom: 5px;
    }
    .action-buttons {
        white-space: nowrap;
    }

    /* 高级筛选样式 (保持) */
    .filter-group {
        border: 1px dashed #ccc;
        padding: 10px;
        border-radius: 5px;
        background-color: #f8f9fa;
        margin-bottom: 15px;
    }
    .filter-group .filter-group {
        margin-left: 0px;
        background-color: #fff;
    }
    .filter-group-header {
        background-color: #f0f0f0;
        padding: 8px 12px;
        border-radius: 4px;
        margin-bottom: 12px;
        font-weight: 500;
    }
    .filter-conditions {
        padding-left: 15px;
    }
    .filter-condition {
        margin-bottom: 8px;
        padding: 8px;
        border-radius: 4px;
        background-color: #fff;
        transition: background-color 0.2s;
    }
    .filter-condition:hover {
        background-color: #f0f0f0;
    }
    .filter-field { min-width: 120px; }
    .filter-operator { min-width: 100px; }
    .filter-value { min-width: 150px; }
    .filter-actions { white-space: nowrap; }

    /* DataTables 搜索框/长度/按钮布局 (保持) */
    .dataTables_length { padding: 5px 0; display: flex; align-items: center; }
    .dt-buttons { float: right; margin-left: 10px; }
    .dataTables_filter { float: left; text-align: left; }
    .dataTables_length { float: right; text-align: right; }
    .dataTables_info { clear: both; padding-top: 8px !important; }
    .dataTables_paginate { float: right; text-align: right; padding-top: 5px !important; }
    .dataTables_info { padding-top: 8px !important; color: #495057; }

    /* 基于模块名的CSS类，用于可能的样式覆盖 */
    .module-{{ config.module_name | default('generic', true) }} {
        /* 特定模块的样式可以写在这里或单独的CSS文件中 */
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid module-{{ config.module_name | default('generic', true) }}">
    <div class="main-content">
        <h2>{{ config.header_title | default('数据管理', true) }}</h2>

        <!-- 操作菜单 (按钮将由JS根据config动态生成/控制显隐) -->
        <div class="operation-bar">
            <button id="btnAdd" class="btn btn-primary">添加</button>
            <button id="btnEdit" class="btn btn-secondary" disabled>编辑</button>
            <button id="btnDelete" class="btn btn-danger" disabled>删除</button>
            <!-- 其他通用批量按钮占位符 -->
            <button id="btnTop" class="btn btn-info" disabled>置顶</button>
            <button id="btnBottom" class="btn btn-info" disabled>置底</button>
            <button id="btnSync" class="btn btn-warning" disabled>标记同步</button>
             <button id="btnUnsync" class="btn btn-warning" disabled>取消同步</button> <!-- 添加取消同步按钮占位符 -->
            <!-- 可选的全局操作按钮容器 -->
             <span id="globalActionsContainer"></span>

            <!-- 列显示控制下拉框 -->
            <div class="btn-group column-toggle-dropdown float-right">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    显示/隐藏列
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <!-- 复选框将由JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 高级筛选区域 -->
        <div id="advancedFilterSection" class="mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">高级筛选</h5>
                    <div>
                        <button id="btnApplyAdvancedFilter" class="btn btn-sm btn-success">应用筛选</button>
                        <button id="btnResetAdvancedFilter" class="btn btn-sm btn-danger">重置</button>
                        <button id="btnTestFilter" class="btn btn-sm btn-info mr-2">测试</button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="filterContainer" class="filter-container">
                        <!-- 顶层条件组容器 -->
                        <div id="topLevelGroups">
                            <!-- 条件组将会动态添加到这里 -->
                        </div>
                        <!-- 添加顶层条件组按钮 -->
                        <div class="text-center mt-3">
                            <button id="btnAddTopLevelGroup" class="btn btn-outline-primary">
                                <i class="fas fa-plus-circle"></i> 添加顶层条件组
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 响应式数据表格 -->
        <div class="table-responsive">
            <table id="dataTable" class="table table-striped table-bordered" width="100%">
                <thead>
                    <tr>
                        <!-- 表头将由JS根据config动态生成部分, 但保留基础结构 -->
                        <th><input type="checkbox" id="selectAll"></th>
                        <!-- 其他表头列占位符 -->
                        <th>操作</th> <!-- 保留操作列标题 -->
                    </tr>
                </thead>
                <tbody>
                    <!-- 数据将通过DataTables动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加/编辑模态框 (通用骨架) -->
<div class="modal fade" id="modelModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">操作</h5> <!-- 通用标题 -->
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="modelForm">
                    <input type="hidden" id="modelId">
                    <!-- 表单字段将由JS根据config动态生成 -->
                    <div id="modalFormFieldsContainer">
                        <!-- e.g., <div class="form-group">...</div> -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btnSaveModel">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 确认操作模态框 (通用骨架) -->
<div class="modal fade" id="confirmModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmTitle">确认操作</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btnConfirm">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- 注入后端配置 -->
<script>
    var moduleConfig = {{ config | tojson | safe }};
    // 可以在这里添加一些基于moduleConfig的初步JS设置（如果需要）
    console.log('Module Config Loaded:', moduleConfig);
</script>

<!-- 引用通用的 DataTables JS -->
<script src="{{ url_for('static', filename='js/generic_datatable.js') }}"></script>

<!-- 后续步骤可能在此处添加特定于页面的、不能放入通用JS的初始化代码 -->

{% endblock %} 