from app.dao.llm_ai_operate_dao import LlmAiOperateDAO
from app.services.api.service_openai import ServiceOpenAI
from flask_login import current_user
from flask import current_app

class PaginationResult:
    """模拟Flask-SQLAlchemy分页结果的类"""
    def __init__(self, items, page, per_page, total):
        self.items = items
        self.page = page
        self.per_page = per_page
        self.total = total
        self.pages = (total + per_page - 1) // per_page if per_page > 0 else 1

class LlmAiOperateService:
    """模型服务类，实现LlmAiOperate模型的业务逻辑"""
    
    def __init__(self):
        """初始化"""
        self.dao = LlmAiOperateDAO()
        self.serviceOpenAI = ServiceOpenAI()
    
    def get_by_id(self, id):
        """根据ID获取记录"""
        return self.dao.get_by_id(id)
    
    def get_by_model_name(self, model_name):
        """根据模型名称获取记录"""
        return self.dao.get_by_model_name(model_name)
    
    def get_all(self, page=1, per_page=20, sort_by='sort_order', order='asc', search_value=None, advanced_filter=None, **filters):
        """获取所有记录，支持分页、排序、搜索和筛选"""
        # 处理"全部"选项，但设置一个最大限制防止性能问题
        if per_page == -1:
            per_page = current_app.config.get('MAX_ITEMS_PER_PAGE', 10000)
        
        # 处理高级筛选条件
        if advanced_filter and isinstance(advanced_filter, dict):
            # 检查是否为多层级结构
            if 'groups' in advanced_filter:
                current_app.logger.debug(f"Using multi-level filter structure: {advanced_filter}")
                
                # 检查是否有有效的组和条件
                has_valid_conditions = False
                for group in advanced_filter.get('groups', []):
                    if group and isinstance(group, dict) and group.get('conditions') and len(group.get('conditions', [])) > 0:
                        has_valid_conditions = True
                        break
                
                if not has_valid_conditions:
                    current_app.logger.warning("Advanced filter has no valid conditions, ignoring")
                    advanced_filter = None
            elif advanced_filter.get('type') != 'group':
                current_app.logger.warning(f"Invalid advanced filter format: {advanced_filter}")
                advanced_filter = None
        
        # 对于超大分页，优化查询
        if per_page > 1000:
            # 获取未分页的查询对象
            query = self.dao.get_all_query(
                sort_by=sort_by,
                order=order,
                search_value=search_value,
                advanced_filter=advanced_filter,
                **filters
            )
            # 获取所有数据
            items = query.all()
            
            # 创建结果字典
            result = {
                'data': [model.to_dict() for model in items],
                'total': len(items),
                'current_page': 1,
                'per_page': len(items),
                'total_pages': 1,
                'has_next': False,
                'has_prev': False
            }
            
            return result
        else:
            # 常规分页查询
            pagination = self.dao.get_all(
                page=page,
                per_page=per_page,
                sort_by=sort_by,
                order=order,
                search_value=search_value,
                advanced_filter=advanced_filter,
                **filters
            )
            
            # 提取结果并转换为字典列表
            result = {
                'data': [model.to_dict() for model in pagination.items],
                'total': pagination.total,
                'current_page': pagination.page,
                'per_page': pagination.per_page,
                'total_pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
            
            return result
    
    def create(self, data):
        """创建新记录"""
        # 添加创建人信息
        if 'create_by' not in data and hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
            data['create_by'] = current_user.username
        return self.dao.create(data)
    
    def update(self, id, data):
        """更新记录"""
        # 添加更新人信息
        if 'update_by' not in data and hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
            data['update_by'] = current_user.username
        return self.dao.update(id, data)
    
    def delete(self, id):
        """删除记录(逻辑删除)"""
        return self.dao.delete(id)
    
    def bulk_delete(self, ids):
        """批量删除记录(逻辑删除)"""
        return self.dao.bulk_delete(ids)
    
    def update_sort_order(self, id, action):
        """更新排序顺序"""
        return self.dao.update_sort_order(id, action)
    
    def batch_update_sort_order(self, ids, action):
        """批量更新排序顺序"""
        return self.dao.batch_update_sort_order(ids, action)
    
    def update_sync_status(self, id, need_sync=1):
        """更新同步状态"""
        return self.dao.update_sync_status(id, need_sync)
    
    def bulk_update_sync_status(self, ids, need_sync=1):
        """批量更新同步状态"""
        return self.dao.bulk_update_sync_status(ids, need_sync)
    
    def fetch_models(self, api_base, api_key):
        """从API获取模型列表"""
        return self.serviceOpenAI.get_models(api_base, api_key)
    
    def import_models(self, models_data, field_name, level_value):
        """从模型数据中导入指定字段的值"""
        
        
        return self.serviceOpenAI.process_json_and_save_to_db(models_data, field_name, level_value)
        
        # # 提取字段值
        # values = self.serviceOpenAI.extract_field_values(models_data, field_name, level_value)
        
        # # 导入到数据库
        # success_count = 0
        # exist_count = 0
        
        # # 获取当前用户信息
        # username = None
        # if hasattr(current_user, 'is_authenticated') and current_user.is_authenticated:
        #     username = current_user.username
        
        # for value in values:
        #     if not value:
        #         continue
                
        #     # 检查是否已存在
        #     existing = self.get_by_model_name(value)
        #     if existing:
        #         exist_count += 1
        #         continue
                
        #     # 创建新记录
        #     model_data = {
        #         'model_name': value,
        #         'need_sync': 0,
        #         'sort_order': 1,
        #         'create_by': username,
        #         'update_by': username
        #     }
            
        #     if self.create(model_data):
        #         success_count += 1
        
        # return {
        #     'total': len(values),
        #     'imported': success_count,
        #     'existed': exist_count
        # }